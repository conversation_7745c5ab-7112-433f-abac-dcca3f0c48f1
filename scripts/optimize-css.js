#!/usr/bin/env node

/**
 * CSS优化脚本
 * 用于生产环境的CSS优化和分析
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const config = {
  srcDir: path.join(__dirname, '../src'),
  distDir: path.join(__dirname, '../dist'),
  stylesDir: path.join(__dirname, '../src/styles'),
  outputDir: path.join(__dirname, '../dist/css'),
  criticalCssFile: path.join(__dirname, '../src/styles/critical.scss'),
  reportFile: path.join(__dirname, '../css-optimization-report.json')
};

// 工具函数
const log = (message, type = 'info') => {
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    warning: '\x1b[33m',
    error: '\x1b[31m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[type]}[CSS优化] ${message}${colors.reset}`);
};

// 分析CSS文件大小
const analyzeCssSize = (filePath) => {
  if (!fs.existsSync(filePath)) return 0;
  const stats = fs.statSync(filePath);
  return stats.size;
};

// 计算CSS压缩率
const calculateCompressionRatio = (originalSize, compressedSize) => {
  if (originalSize === 0) return 0;
  return ((originalSize - compressedSize) / originalSize * 100).toFixed(2);
};

// 扫描CSS文件
const scanCssFiles = (dir) => {
  const files = [];
  const scan = (currentDir) => {
    const items = fs.readdirSync(currentDir);
    items.forEach(item => {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      if (stat.isDirectory()) {
        scan(fullPath);
      } else if (item.endsWith('.scss') || item.endsWith('.css')) {
        files.push({
          path: fullPath,
          relativePath: path.relative(config.srcDir, fullPath),
          size: stat.size,
          name: item
        });
      }
    });
  };
  scan(dir);
  return files;
};

// 生成关键CSS
const generateCriticalCss = () => {
  log('生成关键CSS...');
  
  try {
    // 这里可以集成关键CSS提取工具
    // 例如：critical, penthouse 等
    const criticalCssContent = fs.readFileSync(config.criticalCssFile, 'utf8');
    const outputPath = path.join(config.outputDir, 'critical.css');
    
    // 确保输出目录存在
    if (!fs.existsSync(config.outputDir)) {
      fs.mkdirSync(config.outputDir, { recursive: true });
    }
    
    // 编译关键CSS
    execSync(`npx sass ${config.criticalCssFile} ${outputPath} --style=compressed`, {
      stdio: 'inherit'
    });
    
    const size = analyzeCssSize(outputPath);
    log(`关键CSS生成完成: ${(size / 1024).toFixed(2)}KB`, 'success');
    
    return { path: outputPath, size };
  } catch (error) {
    log(`关键CSS生成失败: ${error.message}`, 'error');
    return null;
  }
};

// 分析未使用的CSS
const analyzeUnusedCss = () => {
  log('分析未使用的CSS...');
  
  // 这里可以集成PurgeCSS或类似工具
  // 扫描HTML/Vue文件中使用的类名
  const usedClasses = new Set();
  const unusedClasses = new Set();
  
  // 简单的类名提取（实际项目中应该使用更完善的工具）
  const extractClassNames = (content) => {
    const classRegex = /class[Name]*\s*=\s*["']([^"']+)["']/g;
    let match;
    while ((match = classRegex.exec(content)) !== null) {
      const classes = match[1].split(/\s+/);
      classes.forEach(cls => {
        if (cls.trim()) {
          usedClasses.add(cls.trim());
        }
      });
    }
  };
  
  // 扫描Vue文件
  const scanVueFiles = (dir) => {
    const items = fs.readdirSync(dir);
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scanVueFiles(fullPath);
      } else if (item.endsWith('.vue') || item.endsWith('.html')) {
        const content = fs.readFileSync(fullPath, 'utf8');
        extractClassNames(content);
      }
    });
  };
  
  scanVueFiles(config.srcDir);
  
  log(`发现 ${usedClasses.size} 个使用的CSS类`, 'info');
  
  return {
    usedClasses: Array.from(usedClasses),
    unusedClasses: Array.from(unusedClasses),
    usageRate: usedClasses.size
  };
};

// 生成优化报告
const generateOptimizationReport = (data) => {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFiles: data.cssFiles.length,
      totalSize: data.totalSize,
      compressedSize: data.compressedSize,
      compressionRatio: data.compressionRatio,
      criticalCssSize: data.criticalCss?.size || 0
    },
    files: data.cssFiles,
    criticalCss: data.criticalCss,
    unusedCss: data.unusedCss,
    recommendations: []
  };
  
  // 生成优化建议
  if (data.compressionRatio < 50) {
    report.recommendations.push({
      type: 'compression',
      message: '建议启用更强的CSS压缩',
      priority: 'high'
    });
  }
  
  if (data.criticalCss && data.criticalCss.size > 14 * 1024) {
    report.recommendations.push({
      type: 'critical-css',
      message: '关键CSS大小超过14KB，建议进一步优化',
      priority: 'medium'
    });
  }
  
  if (data.unusedCss.usageRate < 100) {
    report.recommendations.push({
      type: 'unused-css',
      message: '发现未使用的CSS，建议使用PurgeCSS清理',
      priority: 'medium'
    });
  }
  
  // 保存报告
  fs.writeFileSync(config.reportFile, JSON.stringify(report, null, 2));
  log(`优化报告已生成: ${config.reportFile}`, 'success');
  
  return report;
};

// 主函数
const main = async () => {
  log('开始CSS优化分析...');
  
  try {
    // 扫描CSS文件
    const cssFiles = scanCssFiles(config.stylesDir);
    const totalSize = cssFiles.reduce((sum, file) => sum + file.size, 0);
    
    log(`发现 ${cssFiles.length} 个CSS文件，总大小: ${(totalSize / 1024).toFixed(2)}KB`);
    
    // 生成关键CSS
    const criticalCss = generateCriticalCss();
    
    // 分析未使用的CSS
    const unusedCss = analyzeUnusedCss();
    
    // 模拟压缩后的大小（实际应该通过构建工具获取）
    const compressedSize = Math.round(totalSize * 0.7); // 假设70%的压缩率
    const compressionRatio = calculateCompressionRatio(totalSize, compressedSize);
    
    // 生成报告
    const report = generateOptimizationReport({
      cssFiles,
      totalSize,
      compressedSize,
      compressionRatio,
      criticalCss,
      unusedCss
    });
    
    // 输出摘要
    log('=== 优化摘要 ===', 'info');
    log(`总文件数: ${report.summary.totalFiles}`);
    log(`原始大小: ${(report.summary.totalSize / 1024).toFixed(2)}KB`);
    log(`压缩后大小: ${(report.summary.compressedSize / 1024).toFixed(2)}KB`);
    log(`压缩率: ${report.summary.compressionRatio}%`);
    if (report.summary.criticalCssSize > 0) {
      log(`关键CSS大小: ${(report.summary.criticalCssSize / 1024).toFixed(2)}KB`);
    }
    
    // 输出建议
    if (report.recommendations.length > 0) {
      log('=== 优化建议 ===', 'warning');
      report.recommendations.forEach((rec, index) => {
        log(`${index + 1}. [${rec.priority.toUpperCase()}] ${rec.message}`);
      });
    }
    
    log('CSS优化分析完成!', 'success');
    
  } catch (error) {
    log(`优化过程中出现错误: ${error.message}`, 'error');
    process.exit(1);
  }
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  analyzeCssSize,
  calculateCompressionRatio,
  scanCssFiles,
  generateCriticalCss,
  analyzeUnusedCss,
  generateOptimizationReport
};
