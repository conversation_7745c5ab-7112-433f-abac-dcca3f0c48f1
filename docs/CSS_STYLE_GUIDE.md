# CSS样式指南

本文档描述了项目中CSS样式的架构、约定和最佳实践。

## 📁 文件结构

```
src/styles/
├── variables.scss      # CSS变量和设计令牌
├── mixins.scss        # SCSS混入库
├── utilities.scss     # 原子化工具类
├── components.scss    # 可复用组件样式
├── responsive.scss    # 响应式设计系统
├── performance.scss   # 性能优化样式
├── critical.scss      # 关键CSS（首屏渲染）
└── global.scss        # 全局样式入口
```

## 🎨 设计系统

### 颜色系统

项目使用语义化的颜色系统，支持亮色和暗色主题：

```scss
// 主题色
--primary-color: #0052D9;
--secondary-color: #FF8A00;

// 功能色
--success-color: #52c41a;
--warning-color: #faad14;
--error-color: #ff4d4f;
--info-color: #1890ff;

// 中性色
--text-primary: #262626;
--text-secondary: #595959;
--bg-primary: #ffffff;
--bg-secondary: #fafafa;
```

### 间距系统

基于4px网格的间距系统：

```scss
--spacing-1: 4px;   // 0.25rem
--spacing-2: 8px;   // 0.5rem
--spacing-3: 12px;  // 0.75rem
--spacing-4: 16px;  // 1rem
--spacing-5: 20px;  // 1.25rem
--spacing-6: 24px;  // 1.5rem
```

### 字体系统

```scss
--font-size-xs: 10px;
--font-size-sm: 12px;
--font-size-base: 14px;
--font-size-lg: 16px;
--font-size-xl: 18px;
--font-size-2xl: 20px;
```

## 🧩 组件样式

### 按钮组件

```scss
.btn {
  @include btn-base;
  @include btn-size(base);
  
  &--primary {
    background: var(--primary-color);
    color: var(--text-inverse);
  }
  
  &--sm { @include btn-size(sm); }
  &--lg { @include btn-size(lg); }
}
```

### 输入框组件

```scss
.input {
  @include input-base;
  
  &--error {
    border-color: var(--error-color);
  }
}
```

## 📱 响应式设计

### 断点系统

```scss
$breakpoint-xs: 375px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-2xl: 1400px;
```

### 响应式混入

```scss
@include respond-to(md) {
  // 中等屏幕及以上的样式
}

@include mobile-first(768px) {
  // 移动端优先的响应式样式
}
```

### 网格系统

```scss
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 $spacing-4;
  
  @include respond-to(lg) {
    max-width: 960px;
  }
}

.row {
  display: flex;
  flex-wrap: wrap;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}
```

## 🚀 性能优化

### GPU加速

```scss
.gpu-accelerated {
  @include gpu-accelerated;
}
```

### 内容可见性

```scss
.content-visibility {
  @include content-visibility;
}
```

### 懒加载动画

```scss
.lazy-load {
  @include lazy-load-animation;
}
```

## 🎯 工具类

### 布局工具类

```scss
.flex { display: flex; }
.flex-center { @include flex-center; }
.flex-between { @include flex-between; }
```

### 间距工具类

```scss
.p-4 { padding: $spacing-4; }
.m-4 { margin: $spacing-4; }
.px-4 { padding-left: $spacing-4; padding-right: $spacing-4; }
```

### 文本工具类

```scss
.text-center { text-align: center; }
.text-lg { font-size: $font-size-lg; }
.truncate { @include text-ellipsis(1); }
```

## 🌙 主题系统

### 主题切换

项目支持亮色、暗色和系统主题：

```scss
// 暗色主题
[data-theme="dark"] {
  --text-primary: #ffffff;
  --bg-primary: #141414;
}

// 系统主题检测
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --text-primary: #ffffff;
    --bg-primary: #141414;
  }
}
```

## 📝 编码规范

### 命名约定

1. **BEM方法论**：使用块-元素-修饰符命名
   ```scss
   .card { }
   .card__header { }
   .card--elevated { }
   ```

2. **语义化命名**：使用有意义的类名
   ```scss
   .btn--primary { }  // ✅ 好
   .btn--blue { }     // ❌ 不好
   ```

### 代码组织

1. **按功能分组**：相关的样式放在一起
2. **使用注释**：为复杂的样式添加注释
3. **保持一致性**：遵循项目的编码风格

### 性能最佳实践

1. **避免深层嵌套**：最多3层嵌套
2. **使用高效选择器**：避免通用选择器
3. **合理使用GPU加速**：只在需要时使用
4. **优化关键渲染路径**：内联关键CSS

## 🔧 构建优化

### CSS压缩

生产环境自动启用CSS压缩：

```javascript
// postcss.config.js
module.exports = {
  plugins: {
    autoprefixer: {},
    cssnano: {
      preset: ['default', {
        discardComments: { removeAll: true }
      }]
    }
  }
}
```

### 关键CSS提取

```bash
npm run css:critical  # 生成关键CSS
npm run css:analyze   # 分析CSS使用情况
npm run css:optimize  # 完整优化流程
```

## 📊 性能监控

使用内置的CSS分析工具：

```bash
npm run css:analyze
```

生成的报告包括：
- 文件大小分析
- 压缩率统计
- 未使用CSS检测
- 优化建议

## 🎨 设计令牌

所有设计令牌都定义在 `variables.scss` 中：

```scss
// 颜色令牌
$primary-color: #0052D9;
$success-color: #52c41a;

// 尺寸令牌
$spacing-4: 16px;
$radius-lg: 8px;

// 字体令牌
$font-size-base: 14px;
$font-weight-medium: 500;
```

## 🔄 迁移指南

### 从旧版本迁移

1. **更新导入**：
   ```scss
   // 旧版本
   @import 'styles/variables';
   
   // 新版本
   @use 'styles/variables' as *;
   ```

2. **使用新的混入**：
   ```scss
   // 旧版本
   display: flex;
   align-items: center;
   justify-content: center;
   
   // 新版本
   @include flex-center;
   ```

3. **采用工具类**：
   ```scss
   // 旧版本
   .my-component {
     padding: 16px;
     margin: 8px;
   }
   
   // 新版本
   <div class="p-4 m-2">
   ```

## 📚 参考资源

- [SCSS官方文档](https://sass-lang.com/)
- [BEM方法论](http://getbem.com/)
- [CSS性能优化](https://web.dev/css-performance/)
- [响应式设计指南](https://web.dev/responsive-web-design-basics/)

---

**注意**：本指南会随着项目发展持续更新，请定期查看最新版本。
