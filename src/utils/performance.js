// 性能优化工具函数

/**
 * 图片懒加载管理器
 */
class LazyImageManager {
  constructor() {
    this.observer = null
    this.images = new Set()
    this.init()
  }
  
  init() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            this.loadImage(entry.target)
            this.observer.unobserve(entry.target)
          }
        })
      }, {
        rootMargin: '50px'
      })
    }
  }
  
  observe(element) {
    if (this.observer && element) {
      this.observer.observe(element)
      this.images.add(element)
    }
  }
  
  unobserve(element) {
    if (this.observer && element) {
      this.observer.unobserve(element)
      this.images.delete(element)
    }
  }
  
  loadImage(img) {
    const src = img.dataset.src
    if (src) {
      img.src = src
      img.classList.add('loaded')
      img.removeAttribute('data-src')
    }
  }
  
  destroy() {
    if (this.observer) {
      this.images.forEach(img => this.observer.unobserve(img))
      this.observer.disconnect()
      this.images.clear()
    }
  }
}

// 全局懒加载管理器实例
export const lazyImageManager = new LazyImageManager()

/**
 * 内存缓存管理器
 */
class MemoryCache {
  constructor(maxSize = 50) {
    this.cache = new Map()
    this.maxSize = maxSize
  }
  
  get(key) {
    if (this.cache.has(key)) {
      // 更新访问时间
      const item = this.cache.get(key)
      item.lastAccess = Date.now()
      return item.data
    }
    return null
  }
  
  set(key, data) {
    // 如果缓存已满，删除最久未访问的项
    if (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }
    
    this.cache.set(key, {
      data,
      lastAccess: Date.now(),
      size: this.calculateSize(data)
    })
  }
  
  has(key) {
    return this.cache.has(key)
  }
  
  delete(key) {
    return this.cache.delete(key)
  }
  
  clear() {
    this.cache.clear()
  }
  
  evictLRU() {
    let oldestKey = null
    let oldestTime = Date.now()
    
    for (const [key, item] of this.cache) {
      if (item.lastAccess < oldestTime) {
        oldestTime = item.lastAccess
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }
  
  calculateSize(data) {
    if (typeof data === 'string') {
      return data.length
    }
    return JSON.stringify(data).length
  }
  
  getStats() {
    let totalSize = 0
    for (const item of this.cache.values()) {
      totalSize += item.size
    }
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      totalSize,
      keys: Array.from(this.cache.keys())
    }
  }
}

// 全局内存缓存实例
export const memoryCache = new MemoryCache(100)

/**
 * 本地存储缓存管理器
 */
class StorageCache {
  constructor(prefix = 'ai_dressup_', maxAge = 24 * 60 * 60 * 1000) { // 默认24小时过期
    this.prefix = prefix
    this.maxAge = maxAge
  }
  
  set(key, data, customMaxAge) {
    const item = {
      data,
      timestamp: Date.now(),
      maxAge: customMaxAge || this.maxAge
    }
    
    try {
      uni.setStorageSync(this.prefix + key, JSON.stringify(item))
      return true
    } catch (error) {
      console.error('Storage cache set error:', error)
      return false
    }
  }
  
  get(key) {
    try {
      const itemStr = uni.getStorageSync(this.prefix + key)
      if (!itemStr) return null
      
      const item = JSON.parse(itemStr)
      const now = Date.now()
      
      // 检查是否过期
      if (now - item.timestamp > item.maxAge) {
        this.delete(key)
        return null
      }
      
      return item.data
    } catch (error) {
      console.error('Storage cache get error:', error)
      return null
    }
  }
  
  has(key) {
    return this.get(key) !== null
  }
  
  delete(key) {
    try {
      uni.removeStorageSync(this.prefix + key)
      return true
    } catch (error) {
      console.error('Storage cache delete error:', error)
      return false
    }
  }
  
  clear() {
    try {
      const info = uni.getStorageInfoSync()
      const keysToDelete = info.keys.filter(key => key.startsWith(this.prefix))
      
      keysToDelete.forEach(key => {
        uni.removeStorageSync(key)
      })
      
      return true
    } catch (error) {
      console.error('Storage cache clear error:', error)
      return false
    }
  }
  
  // 清理过期缓存
  cleanup() {
    try {
      const info = uni.getStorageInfoSync()
      const cacheKeys = info.keys.filter(key => key.startsWith(this.prefix))
      
      cacheKeys.forEach(key => {
        const itemStr = uni.getStorageSync(key)
        if (itemStr) {
          try {
            const item = JSON.parse(itemStr)
            const now = Date.now()
            
            if (now - item.timestamp > item.maxAge) {
              uni.removeStorageSync(key)
            }
          } catch (error) {
            // 如果解析失败，删除该项
            uni.removeStorageSync(key)
          }
        }
      })
    } catch (error) {
      console.error('Storage cache cleanup error:', error)
    }
  }
}

// 全局存储缓存实例
export const storageCache = new StorageCache()

/**
 * 图片缓存管理器
 */
class ImageCache {
  constructor() {
    this.cache = new Map()
    this.loading = new Set()
  }
  
  async get(url) {
    // 如果已缓存，直接返回
    if (this.cache.has(url)) {
      return this.cache.get(url)
    }
    
    // 如果正在加载，等待加载完成
    if (this.loading.has(url)) {
      return new Promise((resolve) => {
        const checkLoaded = () => {
          if (this.cache.has(url)) {
            resolve(this.cache.get(url))
          } else if (this.loading.has(url)) {
            setTimeout(checkLoaded, 100)
          } else {
            resolve(null)
          }
        }
        checkLoaded()
      })
    }
    
    // 开始加载图片
    this.loading.add(url)
    
    try {
      const cachedUrl = await this.loadAndCache(url)
      this.loading.delete(url)
      return cachedUrl
    } catch (error) {
      this.loading.delete(url)
      console.error('Image cache load error:', error)
      return url // 返回原始URL作为fallback
    }
  }
  
  async loadAndCache(url) {
    return new Promise((resolve, reject) => {
      // 在UniApp中，我们可以使用downloadFile来缓存图片
      uni.downloadFile({
        url,
        success: (res) => {
          if (res.statusCode === 200) {
            this.cache.set(url, res.tempFilePath)
            resolve(res.tempFilePath)
          } else {
            reject(new Error('Download failed'))
          }
        },
        fail: reject
      })
    })
  }
  
  preload(urls) {
    urls.forEach(url => {
      if (!this.cache.has(url) && !this.loading.has(url)) {
        this.get(url).catch(() => {
          // 忽略预加载错误
        })
      }
    })
  }
  
  clear() {
    this.cache.clear()
    this.loading.clear()
  }
  
  delete(url) {
    this.cache.delete(url)
    this.loading.delete(url)
  }
  
  getStats() {
    return {
      cached: this.cache.size,
      loading: this.loading.size,
      urls: Array.from(this.cache.keys())
    }
  }
}

// 全局图片缓存实例
export const imageCache = new ImageCache()

/**
 * 防抖函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数
 */
export function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 虚拟滚动管理器
 */
export class VirtualScrollManager {
  constructor(options = {}) {
    this.itemHeight = options.itemHeight || 100
    this.containerHeight = options.containerHeight || 500
    this.buffer = options.buffer || 5
    this.data = []
    this.scrollTop = 0
    this.visibleStart = 0
    this.visibleEnd = 0
  }
  
  setData(data) {
    this.data = data
    this.updateVisibleRange()
  }
  
  updateScrollTop(scrollTop) {
    this.scrollTop = scrollTop
    this.updateVisibleRange()
  }
  
  updateVisibleRange() {
    const visibleCount = Math.ceil(this.containerHeight / this.itemHeight)
    this.visibleStart = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.buffer)
    this.visibleEnd = Math.min(this.data.length, this.visibleStart + visibleCount + this.buffer * 2)
  }
  
  getVisibleData() {
    return this.data.slice(this.visibleStart, this.visibleEnd).map((item, index) => ({
      ...item,
      index: this.visibleStart + index,
      top: (this.visibleStart + index) * this.itemHeight
    }))
  }
  
  getTotalHeight() {
    return this.data.length * this.itemHeight
  }
  
  getTransform() {
    return `translateY(${this.visibleStart * this.itemHeight}px)`
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = {
      pageLoadTime: 0,
      apiResponseTimes: [],
      imageLoadTimes: [],
      memoryUsage: [],
      errors: []
    }
  }
  
  recordPageLoad(startTime) {
    this.metrics.pageLoadTime = Date.now() - startTime
  }
  
  recordApiResponse(duration, url) {
    this.metrics.apiResponseTimes.push({
      duration,
      url,
      timestamp: Date.now()
    })
    
    // 只保留最近100条记录
    if (this.metrics.apiResponseTimes.length > 100) {
      this.metrics.apiResponseTimes.shift()
    }
  }
  
  recordImageLoad(duration, url) {
    this.metrics.imageLoadTimes.push({
      duration,
      url,
      timestamp: Date.now()
    })
    
    if (this.metrics.imageLoadTimes.length > 100) {
      this.metrics.imageLoadTimes.shift()
    }
  }
  
  recordError(error, context) {
    this.metrics.errors.push({
      error: error.message || error,
      context,
      timestamp: Date.now()
    })
    
    if (this.metrics.errors.length > 50) {
      this.metrics.errors.shift()
    }
  }
  
  getMetrics() {
    return {
      ...this.metrics,
      averageApiResponseTime: this.calculateAverage(this.metrics.apiResponseTimes.map(item => item.duration)),
      averageImageLoadTime: this.calculateAverage(this.metrics.imageLoadTimes.map(item => item.duration))
    }
  }
  
  calculateAverage(numbers) {
    if (numbers.length === 0) return 0
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor()

/**
 * 初始化性能优化
 */
export function initPerformanceOptimization() {
  // 清理过期缓存
  storageCache.cleanup()
  
  // 监听内存警告
  if (typeof uni !== 'undefined') {
    uni.onMemoryWarning && uni.onMemoryWarning(() => {
      console.warn('Memory warning detected, clearing caches...')
      memoryCache.clear()
      imageCache.clear()
    })
  }
  
  // 定期清理缓存
  setInterval(() => {
    storageCache.cleanup()
  }, 30 * 60 * 1000) // 每30分钟清理一次
}

export default {
  lazyImageManager,
  memoryCache,
  storageCache,
  imageCache,
  debounce,
  throttle,
  VirtualScrollManager,
  performanceMonitor,
  initPerformanceOptimization
}