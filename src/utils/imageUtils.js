// 图片处理工具函数

/**
 * 压缩图片
 * @param {File} file 原始图片文件
 * @param {number} quality 压缩质量 0-1
 * @param {number} maxWidth 最大宽度
 * @returns {Promise<string>} 压缩后的base64图片
 */
export function compressImage(file, quality = 0.8, maxWidth = 800) {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img
      if (width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      canvas.width = width
      canvas.height = height
      
      // 绘制压缩后的图片
      ctx.drawImage(img, 0, 0, width, height)
      
      // 转换为base64
      const compressedDataUrl = canvas.toDataURL('image/jpeg', quality)
      resolve(compressedDataUrl)
    }
    
    img.onerror = reject
    img.src = URL.createObjectURL(file)
  })
}

/**
 * 获取图片信息
 * @param {string} imagePath 图片路径
 * @returns {Promise<object>} 图片信息
 */
export function getImageInfo(imagePath) {
  return new Promise((resolve, reject) => {
    uni.getImageInfo({
      src: imagePath,
      success: (res) => {
        resolve({
          width: res.width,
          height: res.height,
          path: res.path,
          orientation: res.orientation,
          type: res.type
        })
      },
      fail: reject
    })
  })
}

/**
 * 选择图片
 * @param {object} options 选择选项
 * @returns {Promise<Array>} 选择的图片列表
 */
export function chooseImage(options = {}) {
  const defaultOptions = {
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera']
  }
  
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      ...defaultOptions,
      ...options,
      success: (res) => {
        resolve(res.tempFilePaths)
      },
      fail: reject
    })
  })
}

/**
 * 保存图片到相册
 * @param {string} filePath 图片路径
 * @returns {Promise<void>}
 */
export function saveImageToPhotosAlbum(filePath) {
  return new Promise((resolve, reject) => {
    uni.saveImageToPhotosAlbum({
      filePath,
      success: resolve,
      fail: reject
    })
  })
}

/**
 * 预览图片
 * @param {Array} urls 图片URL数组
 * @param {number} current 当前显示图片索引
 */
export function previewImage(urls, current = 0) {
  uni.previewImage({
    urls,
    current: typeof current === 'number' ? urls[current] : current
  })
}

/**
 * 人体检测和关键点识别（模拟AI功能）
 * @param {string} imagePath 图片路径
 * @returns {Promise<object>} 检测结果
 */
export function detectHumanPose(imagePath) {
  return new Promise((resolve) => {
    // 模拟AI检测过程
    setTimeout(() => {
      resolve({
        success: true,
        confidence: 0.95,
        keypoints: [
          { name: 'head', x: 0.5, y: 0.15, confidence: 0.98 },
          { name: 'neck', x: 0.5, y: 0.25, confidence: 0.96 },
          { name: 'left_shoulder', x: 0.4, y: 0.3, confidence: 0.94 },
          { name: 'right_shoulder', x: 0.6, y: 0.3, confidence: 0.94 },
          { name: 'left_elbow', x: 0.35, y: 0.45, confidence: 0.92 },
          { name: 'right_elbow', x: 0.65, y: 0.45, confidence: 0.92 },
          { name: 'left_wrist', x: 0.3, y: 0.6, confidence: 0.90 },
          { name: 'right_wrist', x: 0.7, y: 0.6, confidence: 0.90 },
          { name: 'left_hip', x: 0.45, y: 0.65, confidence: 0.93 },
          { name: 'right_hip', x: 0.55, y: 0.65, confidence: 0.93 },
          { name: 'left_knee', x: 0.43, y: 0.8, confidence: 0.91 },
          { name: 'right_knee', x: 0.57, y: 0.8, confidence: 0.91 },
          { name: 'left_ankle', x: 0.41, y: 0.95, confidence: 0.89 },
          { name: 'right_ankle', x: 0.59, y: 0.95, confidence: 0.89 }
        ],
        bodySegmentation: {
          mask: 'base64_mask_data',
          boundingBox: {
            x: 0.2,
            y: 0.1,
            width: 0.6,
            height: 0.85
          }
        }
      })
    }, 2000)
  })
}

/**
 * AI虚拟换装（模拟功能）
 * @param {string} personImage 人物图片
 * @param {string} clothesImage 服装图片
 * @param {object} poseData 人体姿态数据
 * @returns {Promise<string>} 换装后的图片
 */
export function virtualTryOn(personImage, clothesImage, poseData) {
  return new Promise((resolve) => {
    // 模拟AI换装过程
    setTimeout(() => {
      // 实际应用中这里会调用AI服务
      // 现在返回一个模拟的结果图片
      resolve(personImage) // 暂时返回原图
    }, 3000)
  })
}

/**
 * 图片滤镜处理
 * @param {string} imagePath 图片路径
 * @param {string} filterType 滤镜类型
 * @returns {Promise<string>} 处理后的图片
 */
export function applyImageFilter(imagePath, filterType = 'none') {
  return new Promise((resolve) => {
    // 模拟滤镜处理
    setTimeout(() => {
      resolve(imagePath)
    }, 1000)
  })
}

/**
 * 生成缩略图
 * @param {string} imagePath 原图路径
 * @param {number} size 缩略图尺寸
 * @returns {Promise<string>} 缩略图路径
 */
export function generateThumbnail(imagePath, size = 200) {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      canvas.width = size
      canvas.height = size
      
      // 计算裁剪区域（居中裁剪）
      const { width, height } = img
      const minSize = Math.min(width, height)
      const startX = (width - minSize) / 2
      const startY = (height - minSize) / 2
      
      ctx.drawImage(img, startX, startY, minSize, minSize, 0, 0, size, size)
      
      const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8)
      resolve(thumbnailDataUrl)
    }
    
    img.onerror = reject
    img.src = imagePath
  })
}