<script>
import { themeManager } from '@/utils/theme'

export default {
  onLaunch: function () {
    console.log('App Launch')

    // 初始化主题系统
    this.initTheme()

    // 预加载主题资源
    themeManager.preloadThemeAssets()
  },
  onShow: function () {
    console.log('App Show')

    // 重新检测系统主题（用户可能在设置中更改了系统主题）
    themeManager.detectSystemTheme()
    themeManager.applyTheme()
  },
  onHide: function () {
    console.log('App Hide')
  },
  methods: {
    initTheme() {
      // 主题系统已在themeManager中自动初始化
      console.log('Theme initialized:', themeManager.getCurrentTheme())

      // 监听主题变化
      themeManager.addListener((themeInfo) => {
        console.log('Theme changed:', themeInfo)

        // 可以在这里处理全局主题变化逻辑
        this.onThemeChange(themeInfo)
      })
    },

    onThemeChange(themeInfo) {
      // 更新页面背景色
      const colors = themeManager.getThemeColors()

      // 设置页面背景
      if (typeof uni !== 'undefined') {
        try {
          uni.setBackgroundColor({
            backgroundColor: colors.background
          })
        } catch (error) {
          // 某些平台可能不支持
        }
      }

      // 触发全局事件
      uni.$emit('themeChange', themeInfo)
    }
  }
}
</script>

<style lang="scss">
// 引入全局样式
@use './styles/global';
@use './styles/performance';

// 每个页面公共css
page {
  background-color: var(--bg-secondary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: var(--text-primary);
  transition: background-color var(--transition-base), color var(--transition-base);
}

// 自定义导航栏通用样式
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: var(--bg-overlay);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--divider-color);
  transition: all var(--transition-base);

  // 暗色主题优化
  [data-theme="dark"] & {
    background: rgba(20, 20, 20, 0.95);
  }
}

// 通用动画
.fade-enter-active, .fade-leave-active {
  transition: opacity var(--transition-base) ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active, .slide-up-leave-active {
  transition: transform var(--transition-base) ease;
}

.slide-up-enter-from, .slide-up-leave-to {
  transform: translateY(100%);
}

// 页面切换动画
.page-enter-active, .page-leave-active {
  transition: all var(--transition-base) ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}

// 全局加载状态
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-toast);

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
  }
}

// 全局错误状态
.global-error {
  padding: var(--spacing-6);
  text-align: center;
  color: var(--text-secondary);

  .error-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-4);
  }

  .error-message {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-4);
  }

  .error-action {
    margin-top: var(--spacing-4);
  }
}

// 性能优化类
.performance-optimized {
  contain: layout style paint;
  will-change: transform;
}

// 关键渲染路径优化
.critical-content {
  // 关键内容优先渲染
  contain: layout;
}

.non-critical-content {
  // 非关键内容延迟渲染
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}
</style>
