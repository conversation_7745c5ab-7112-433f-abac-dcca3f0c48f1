// 关键CSS - 首屏渲染必需的样式
// 这个文件包含首屏渲染必需的最小CSS集合

// ==================== 基础重置 ====================

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #262626;
  background-color: #fafafa;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// ==================== 关键变量 ====================

:root {
  // 最关键的颜色变量
  --primary-color: #0052D9;
  --text-primary: #262626;
  --text-secondary: #595959;
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --border-color: #d9d9d9;
  
  // 关键尺寸
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --radius-2xl: 16px;
  
  // 关键层级
  --z-fixed: 1030;
  --z-modal: 1050;
  
  // 关键过渡
  --transition-base: 0.2s ease;
}

// ==================== 首屏布局 ====================

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// ==================== 导航栏关键样式 ====================

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  height: 60px;
  transition: all var(--transition-base);
}

.navbar__content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-4);
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

// ==================== 按钮关键样式 ====================

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  border: 1px solid transparent;
  border-radius: var(--radius-2xl);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.25;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.btn--primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: #ffffff;
}

// ==================== 加载状态 ====================

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f0f0f0;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// ==================== 骨架屏 ====================

.skeleton {
  background: linear-gradient(90deg, 
    #f0f0f0 25%, 
    #e0e0e0 50%, 
    #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

// ==================== 响应式关键断点 ====================

@media (max-width: 768px) {
  .navbar__content {
    padding: 0 var(--spacing-3);
  }
  
  .btn {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: 13px;
  }
  
  body {
    font-size: 13px;
  }
}

// ==================== 暗色主题关键变量 ====================

@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #d9d9d9;
    --bg-primary: #141414;
    --bg-secondary: #1f1f1f;
    --border-color: #434343;
  }
  
  .navbar {
    background: rgba(20, 20, 20, 0.95);
  }
  
  .skeleton {
    background: linear-gradient(90deg, 
      #262626 25%, 
      #303030 50%, 
      #262626 75%);
    background-size: 200% 100%;
  }
}

// ==================== 性能优化 ====================

// GPU加速
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

// 内容包含
.contain-layout {
  contain: layout;
}

.contain-paint {
  contain: paint;
}

.contain-style {
  contain: style;
}

// 字体加载优化
@font-face {
  font-family: 'PingFang SC';
  font-display: swap;
  src: local('PingFang SC');
}

// ==================== 无障碍支持 ====================

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 高对比度模式
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-primary: #000000;
    --bg-primary: #ffffff;
  }
  
  [data-theme="dark"] {
    --border-color: #ffffff;
    --text-primary: #ffffff;
    --bg-primary: #000000;
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// ==================== 安全区域适配 ====================

.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
