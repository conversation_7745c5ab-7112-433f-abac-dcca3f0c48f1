// 性能优化样式
// 关键渲染路径优化

// 预加载关键字体
@font-face {
  font-family: 'PingFang SC';
  font-display: swap;
  src: local('PingFang SC');
}

// 关键CSS内联
.critical-above-fold {
  // 首屏关键样式
  .hero-section {
    min-height: 100vh;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: var(--bg-overlay);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--divider-color);
    height: 60px;
  }
  
  .loading-skeleton {
    background: linear-gradient(90deg, 
      var(--bg-tertiary) 25%, 
      var(--bg-secondary) 50%, 
      var(--bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
}

// GPU加速优化
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.gpu-layer {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
}

// 图片优化
.optimized-image {
  object-fit: cover;
  object-position: center;
  
  // 渐进式加载
  &.loading {
    filter: blur(5px);
    transition: filter 0.3s ease;
  }
  
  &.loaded {
    filter: blur(0);
  }
  
  // WebP支持检测
  .webp & {
    // WebP格式优化
  }
  
  .no-webp & {
    // 降级到JPEG/PNG
  }
}

// 虚拟滚动优化
.virtual-scroll-container {
  overflow: auto;
  height: 100%;
  
  .virtual-item {
    contain: layout style paint;
    transform: translateZ(0);
  }
}

// 懒加载优化
.lazy-load {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
  
  &.in-view {
    opacity: 1;
    transform: translateY(0);
  }
}

// 减少重绘重排
.no-reflow {
  contain: layout;
  
  .dynamic-content {
    contain: layout style paint;
  }
}

// 内容可见性优化
.content-visibility {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

// 关键资源预加载提示
.preload-hint {
  // 预加载关键图片
  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    background-image: url('/static/critical-image.webp');
    background-size: 0;
  }
}

// 动画性能优化
.optimized-animation {
  // 只使用transform和opacity进行动画
  transition: transform var(--transition-base), opacity var(--transition-base);
  
  // 避免触发layout的属性
  &:not(.no-animate) {
    will-change: transform, opacity;
  }
  
  &.animate-complete {
    will-change: auto;
  }
}

// 滚动性能优化
.smooth-scroll {
  scroll-behavior: smooth;
  
  // 在移动设备上使用硬件加速
  @media (max-width: 768px) {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
  }
}

// 内存优化
.memory-efficient {
  // 限制阴影复杂度
  .simple-shadow {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  // 减少渐变复杂度
  .simple-gradient {
    background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
  }
}

// 渲染层优化
.render-layer {
  // 创建新的渲染层
  transform: translateZ(0);
  
  // 或者使用will-change
  &.will-change {
    will-change: transform;
  }
  
  // 动画完成后清理
  &.animation-done {
    will-change: auto;
  }
}

// 字体加载优化
.font-loading {
  // 字体加载期间的降级
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  
  // 字体加载完成后
  .fonts-loaded & {
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  }
}

// 关键CSS分离
@media print {
  // 打印样式优化
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

// 预连接优化提示
.preconnect-hint {
  // 通过CSS提示预连接到关键域名
  &::after {
    content: '';
    background-image: url('//api.example.com/preconnect');
    display: none;
  }
}

// 资源优先级提示
.resource-hints {
  // 高优先级资源
  .high-priority {
    // 关键资源标记
  }
  
  // 低优先级资源
  .low-priority {
    // 延迟加载标记
  }
}

// 缓存优化
.cache-optimized {
  // 长期缓存的静态资源
  .static-asset {
    // 版本化资源标记
  }
  
  // 短期缓存的动态内容
  .dynamic-content {
    // 动态内容标记
  }
}

// 网络优化
.network-aware {
  // 慢网络优化
  @media (max-width: 768px) {
    .slow-network {
      // 减少动画
      animation: none !important;
      transition: none !important;
    }
  }
  
  // 快网络增强
  @media (min-width: 1200px) {
    .fast-network {
      // 增强动画效果
      transition: all var(--transition-slow);
    }
  }
}

// 电池优化
@media (prefers-reduced-motion: reduce) {
  .battery-aware {
    // 减少动画以节省电池
    animation: none !important;
    transition: none !important;
  }
}

// 数据节省模式
@media (prefers-reduced-data: reduce) {
  .data-saver {
    // 减少背景图片
    background-image: none !important;
    
    // 使用简单颜色替代渐变
    background: var(--bg-primary) !important;
  }
}
