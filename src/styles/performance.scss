// 性能优化样式 - 优化版本
// 关键渲染路径优化

// ==================== 字体优化 ====================

// 预加载关键字体
@font-face {
  font-family: 'PingFang SC';
  font-display: swap;
  src: local('PingFang SC');
}

// 字体加载优化
.font-loading {
  font-family: $font-family-base;

  .fonts-loaded & {
    font-family: 'PingFang SC', $font-family-base;
  }
}

// ==================== 关键CSS内联 ====================

.critical-above-fold {
  // 首屏关键样式
  .hero-section {
    min-height: 100vh;
    background: var(--bg-secondary);
    @include flex-center;
    @include contain(layout style);
  }

  .navbar {
    @extend .navbar;
  }

  .loading-skeleton {
    background: linear-gradient(90deg,
      var(--bg-tertiary) 25%,
      var(--bg-secondary) 50%,
      var(--bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    @include contain(layout style paint);
  }
}

// ==================== GPU加速优化 ====================

.gpu-accelerated {
  @include gpu-accelerated;
}

.gpu-layer {
  transform: $gpu-3d;
  backface-visibility: hidden;
  perspective: 1000px;
}

// ==================== 图片优化 ====================

.optimized-image {
  object-fit: cover;
  object-position: center;
  @include contain(layout style);

  // 渐进式加载
  &.loading {
    filter: blur(5px);
    @include transition(filter, 0.3s);
  }

  &.loaded {
    filter: blur(0);
  }

  // WebP支持检测
  .webp & {
    // WebP格式优化 - 减少文件大小
    image-rendering: -webkit-optimize-contrast;
  }

  .no-webp & {
    // 降级到JPEG/PNG
    image-rendering: auto;
  }

  // 懒加载优化
  &[loading="lazy"] {
    @include lazy-load-animation;
  }
}

// ==================== 虚拟滚动优化 ====================

.virtual-scroll-container {
  overflow: auto;
  height: 100%;
  @include custom-scrollbar;

  .virtual-item {
    @include contain(layout style paint);
    @include gpu-accelerated;
  }
}

// ==================== 懒加载优化 ====================

.lazy-load {
  @include lazy-load-animation;
}

// 交叉观察器优化
.intersection-observer {
  @include content-visibility;
}

// ==================== 重绘重排优化 ====================

.no-reflow {
  @include contain(layout);

  .dynamic-content {
    @include contain(layout style paint);
  }
}

// 内容可见性优化
.content-visibility {
  @include content-visibility;
}

// 批量DOM操作优化
.batch-update {
  @include contain(strict);
  @include gpu-accelerated;
}

// 关键资源预加载提示
.preload-hint {
  // 预加载关键图片
  &::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    background-image: url('/static/critical-image.webp');
    background-size: 0;
  }
}

// 动画性能优化
.optimized-animation {
  // 只使用transform和opacity进行动画
  transition: transform var(--transition-base), opacity var(--transition-base);
  
  // 避免触发layout的属性
  &:not(.no-animate) {
    will-change: transform, opacity;
  }
  
  &.animate-complete {
    will-change: auto;
  }
}

// 滚动性能优化
.smooth-scroll {
  scroll-behavior: smooth;
  
  // 在移动设备上使用硬件加速
  @media (max-width: 768px) {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
  }
}

// 内存优化
.memory-efficient {
  // 限制阴影复杂度
  .simple-shadow {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  // 减少渐变复杂度
  .simple-gradient {
    background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
  }
}

// 渲染层优化
.render-layer {
  // 创建新的渲染层
  transform: translateZ(0);
  
  // 或者使用will-change
  &.will-change {
    will-change: transform;
  }
  
  // 动画完成后清理
  &.animation-done {
    will-change: auto;
  }
}

// 字体加载优化
.font-loading {
  // 字体加载期间的降级
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  
  // 字体加载完成后
  .fonts-loaded & {
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  }
}

// 关键CSS分离
@media print {
  // 打印样式优化
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

// 预连接优化提示
.preconnect-hint {
  // 通过CSS提示预连接到关键域名
  &::after {
    content: '';
    background-image: url('//api.example.com/preconnect');
    display: none;
  }
}

// 资源优先级提示
.resource-hints {
  // 高优先级资源
  .high-priority {
    // 关键资源标记
  }
  
  // 低优先级资源
  .low-priority {
    // 延迟加载标记
  }
}

// 缓存优化
.cache-optimized {
  // 长期缓存的静态资源
  .static-asset {
    // 版本化资源标记
  }
  
  // 短期缓存的动态内容
  .dynamic-content {
    // 动态内容标记
  }
}

// 网络优化
.network-aware {
  // 慢网络优化
  @media (max-width: 768px) {
    .slow-network {
      // 减少动画
      animation: none !important;
      transition: none !important;
    }
  }
  
  // 快网络增强
  @media (min-width: 1200px) {
    .fast-network {
      // 增强动画效果
      transition: all var(--transition-slow);
    }
  }
}

// 电池优化
@media (prefers-reduced-motion: reduce) {
  .battery-aware {
    // 减少动画以节省电池
    animation: none !important;
    transition: none !important;
  }
}

// 数据节省模式
@media (prefers-reduced-data: reduce) {
  .data-saver {
    // 减少背景图片
    background-image: none !important;
    
    // 使用简单颜色替代渐变
    background: var(--bg-primary) !important;
  }
}
