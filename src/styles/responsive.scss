// 响应式设计系统 - 优化版本
// 提供完整的响应式解决方案

// ==================== 响应式容器 ====================

.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 $spacing-4;
  
  @include respond-to(sm) {
    max-width: 540px;
    padding: 0 $spacing-5;
  }
  
  @include respond-to(md) {
    max-width: 720px;
    padding: 0 $spacing-6;
  }
  
  @include respond-to(lg) {
    max-width: 960px;
    padding: 0 $spacing-8;
  }
  
  @include respond-to(xl) {
    max-width: 1140px;
    padding: 0 $spacing-10;
  }
  
  @include respond-to(2xl) {
    max-width: 1320px;
  }
}

// ==================== 响应式网格系统 ====================

.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: calc($spacing-3 * -1);
  margin-right: calc($spacing-3 * -1);
}

.col {
  flex: 1;
  padding-left: $spacing-3;
  padding-right: $spacing-3;
  min-width: 0; // 防止flex项目溢出
}

// 生成响应式列类
@for $i from 1 through 12 {
  .col-#{$i} {
    flex: 0 0 percentage($i / 12);
    max-width: percentage($i / 12);
    padding-left: $spacing-3;
    padding-right: $spacing-3;
  }
  
  @include respond-to(sm) {
    .col-sm-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  @include respond-to(md) {
    .col-md-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  @include respond-to(lg) {
    .col-lg-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
  
  @include respond-to(xl) {
    .col-xl-#{$i} {
      flex: 0 0 percentage($i / 12);
      max-width: percentage($i / 12);
    }
  }
}

// ==================== 响应式显示工具类 ====================

// 超小屏幕隐藏/显示
@include respond-to(xs) {
  .hide-xs { display: none !important; }
  .show-xs { display: block !important; }
}

// 小屏幕隐藏/显示
@include respond-to(sm) {
  .hide-sm { display: none !important; }
  .show-sm { display: block !important; }
  .hide-xs { display: block !important; }
  .show-xs { display: none !important; }
}

// 中等屏幕隐藏/显示
@include respond-to(md) {
  .hide-md { display: none !important; }
  .show-md { display: block !important; }
  .hide-sm { display: block !important; }
  .show-sm { display: none !important; }
}

// 大屏幕隐藏/显示
@include respond-to(lg) {
  .hide-lg { display: none !important; }
  .show-lg { display: block !important; }
  .hide-md { display: block !important; }
  .show-md { display: none !important; }
}

// 超大屏幕隐藏/显示
@include respond-to(xl) {
  .hide-xl { display: none !important; }
  .show-xl { display: block !important; }
  .hide-lg { display: block !important; }
  .show-lg { display: none !important; }
}

// 兼容性类
@media (max-width: #{$breakpoint-md - 1px}) {
  .hide-mobile { display: none !important; }
  .show-mobile { display: block !important; }
}

@media (min-width: #{$breakpoint-md}) {
  .hide-desktop { display: none !important; }
  .show-desktop { display: block !important; }
  .hide-mobile { display: block !important; }
  .show-mobile { display: none !important; }
}

// ==================== 响应式间距 ====================

// 响应式padding
@include respond-to(xs) {
  .p-xs-0 { padding: 0 !important; }
  .p-xs-1 { padding: $spacing-1 !important; }
  .p-xs-2 { padding: $spacing-2 !important; }
  .p-xs-3 { padding: $spacing-3 !important; }
  .p-xs-4 { padding: $spacing-4 !important; }
  
  .px-xs-0 { padding-left: 0 !important; padding-right: 0 !important; }
  .px-xs-1 { padding-left: $spacing-1 !important; padding-right: $spacing-1 !important; }
  .px-xs-2 { padding-left: $spacing-2 !important; padding-right: $spacing-2 !important; }
  .px-xs-3 { padding-left: $spacing-3 !important; padding-right: $spacing-3 !important; }
  .px-xs-4 { padding-left: $spacing-4 !important; padding-right: $spacing-4 !important; }
  
  .py-xs-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
  .py-xs-1 { padding-top: $spacing-1 !important; padding-bottom: $spacing-1 !important; }
  .py-xs-2 { padding-top: $spacing-2 !important; padding-bottom: $spacing-2 !important; }
  .py-xs-3 { padding-top: $spacing-3 !important; padding-bottom: $spacing-3 !important; }
  .py-xs-4 { padding-top: $spacing-4 !important; padding-bottom: $spacing-4 !important; }
}

@include respond-to(sm) {
  .p-sm-0 { padding: 0 !important; }
  .p-sm-1 { padding: $spacing-1 !important; }
  .p-sm-2 { padding: $spacing-2 !important; }
  .p-sm-3 { padding: $spacing-3 !important; }
  .p-sm-4 { padding: $spacing-4 !important; }
  .p-sm-5 { padding: $spacing-5 !important; }
  .p-sm-6 { padding: $spacing-6 !important; }
}

@include respond-to(md) {
  .p-md-0 { padding: 0 !important; }
  .p-md-1 { padding: $spacing-1 !important; }
  .p-md-2 { padding: $spacing-2 !important; }
  .p-md-3 { padding: $spacing-3 !important; }
  .p-md-4 { padding: $spacing-4 !important; }
  .p-md-5 { padding: $spacing-5 !important; }
  .p-md-6 { padding: $spacing-6 !important; }
  .p-md-8 { padding: $spacing-8 !important; }
}

// ==================== 响应式字体 ====================

@include respond-to(xs) {
  .text-xs-sm { font-size: $font-size-sm !important; }
  .text-xs-base { font-size: $font-size-base !important; }
  .text-xs-lg { font-size: $font-size-lg !important; }
}

@include respond-to(sm) {
  .text-sm-sm { font-size: $font-size-sm !important; }
  .text-sm-base { font-size: $font-size-base !important; }
  .text-sm-lg { font-size: $font-size-lg !important; }
  .text-sm-xl { font-size: $font-size-xl !important; }
}

@include respond-to(md) {
  .text-md-base { font-size: $font-size-base !important; }
  .text-md-lg { font-size: $font-size-lg !important; }
  .text-md-xl { font-size: $font-size-xl !important; }
  .text-md-2xl { font-size: $font-size-2xl !important; }
}

@include respond-to(lg) {
  .text-lg-lg { font-size: $font-size-lg !important; }
  .text-lg-xl { font-size: $font-size-xl !important; }
  .text-lg-2xl { font-size: $font-size-2xl !important; }
  .text-lg-3xl { font-size: $font-size-3xl !important; }
}

// ==================== 响应式Flexbox ====================

@include respond-to(xs) {
  .flex-xs-col { flex-direction: column !important; }
  .flex-xs-row { flex-direction: row !important; }
  .justify-xs-start { justify-content: flex-start !important; }
  .justify-xs-center { justify-content: center !important; }
  .justify-xs-end { justify-content: flex-end !important; }
}

@include respond-to(sm) {
  .flex-sm-col { flex-direction: column !important; }
  .flex-sm-row { flex-direction: row !important; }
  .justify-sm-start { justify-content: flex-start !important; }
  .justify-sm-center { justify-content: center !important; }
  .justify-sm-end { justify-content: flex-end !important; }
  .justify-sm-between { justify-content: space-between !important; }
}

@include respond-to(md) {
  .flex-md-col { flex-direction: column !important; }
  .flex-md-row { flex-direction: row !important; }
  .justify-md-start { justify-content: flex-start !important; }
  .justify-md-center { justify-content: center !important; }
  .justify-md-end { justify-content: flex-end !important; }
  .justify-md-between { justify-content: space-between !important; }
}

// ==================== 移动端优化 ====================

@media (max-width: #{$breakpoint-md - 1px}) {
  // 移动端导航优化
  .navbar {
    height: 56px; // 移动端导航栏高度
    
    &__content {
      padding: 0 $spacing-3;
    }
    
    &__brand {
      font-size: $font-size-lg;
    }
  }
  
  // 移动端按钮优化
  .btn {
    min-height: 44px; // 符合移动端触摸标准
    padding: $spacing-3 $spacing-4;
    
    &--sm {
      min-height: 36px;
      padding: $spacing-2 $spacing-3;
    }
    
    &--lg {
      min-height: 52px;
      padding: $spacing-4 $spacing-6;
    }
  }
  
  // 移动端输入框优化
  .input {
    min-height: 44px;
    font-size: 16px; // 防止iOS缩放
  }
  
  // 移动端卡片优化
  .card {
    margin: 0 $spacing-3;
    border-radius: $radius-xl;
    
    &__body {
      padding: $spacing-4;
    }
  }
  
  // 移动端模态框优化
  .modal {
    &__content {
      margin: $spacing-4;
      max-height: calc(100vh - #{$spacing-8});
    }
  }
}

// ==================== 平板端优化 ====================

@media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) {
  .container {
    padding: 0 $spacing-6;
  }
  
  .card {
    &__body {
      padding: $spacing-5;
    }
  }
}

// ==================== 桌面端优化 ====================

@media (min-width: #{$breakpoint-lg}) {
  // 桌面端悬停效果
  .btn:hover {
    @include hover-lift;
  }
  
  .card:hover {
    @include hover-lift;
  }
  
  // 桌面端导航优化
  .navbar {
    &__nav {
      gap: $spacing-8;
    }
  }
}
