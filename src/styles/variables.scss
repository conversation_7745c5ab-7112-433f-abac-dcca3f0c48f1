// CSS变量系统 - 统一管理所有设计令牌
// 这个文件会被自动导入到所有SCSS文件中

// ==================== 颜色系统 ====================

// 主题色彩
$primary-color: #0052D9;
$primary-light: #366ef4;
$primary-dark: #003cab;
$primary-hover: #1a6dff;
$primary-active: #0041a3;
$primary-disabled: #a6c8ff;

$secondary-color: #FF8A00;
$secondary-light: #FFB366;
$secondary-dark: #E37318;
$secondary-hover: #ff9f33;
$secondary-active: #cc6e00;
$secondary-disabled: #ffd9b3;

// 功能色彩
$success-color: #52c41a;
$success-light: #73d13d;
$success-dark: #389e0d;

$warning-color: #faad14;
$warning-light: #ffc53d;
$warning-dark: #d48806;

$error-color: #ff4d4f;
$error-light: #ff7875;
$error-dark: #cf1322;

$info-color: #1890ff;
$info-light: #40a9ff;
$info-dark: #096dd9;

// 中性色彩 - 亮色主题
$text-primary: #262626;
$text-secondary: #595959;
$text-tertiary: #8c8c8c;
$text-quaternary: #bfbfbf;
$text-placeholder: #d9d9d9;
$text-disabled: #f5f5f5;
$text-inverse: #ffffff;

$bg-primary: #ffffff;
$bg-secondary: #fafafa;
$bg-tertiary: #f5f5f5;
$bg-quaternary: #f0f0f0;
$bg-disabled: #f5f5f5;
$bg-mask: rgba(0, 0, 0, 0.45);
$bg-overlay: rgba(255, 255, 255, 0.95);

$border-color: #d9d9d9;
$border-color-light: #f0f0f0;
$border-color-dark: #bfbfbf;
$divider-color: #f0f0f0;

// 暗色主题色彩
$dark-text-primary: #ffffff;
$dark-text-secondary: #d9d9d9;
$dark-text-tertiary: #bfbfbf;
$dark-text-quaternary: #8c8c8c;
$dark-text-placeholder: #595959;
$dark-text-disabled: #434343;
$dark-text-inverse: #000000;

$dark-bg-primary: #141414;
$dark-bg-secondary: #1f1f1f;
$dark-bg-tertiary: #262626;
$dark-bg-quaternary: #2f2f2f;
$dark-bg-disabled: #262626;
$dark-bg-mask: rgba(0, 0, 0, 0.65);
$dark-bg-overlay: rgba(20, 20, 20, 0.95);

$dark-border-color: #434343;
$dark-border-color-light: #303030;
$dark-border-color-dark: #595959;
$dark-divider-color: #303030;

// ==================== 尺寸系统 ====================

// 间距系统 (基于4px网格)
$spacing-0: 0px;
$spacing-1: 4px;
$spacing-2: 8px;
$spacing-3: 12px;
$spacing-4: 16px;
$spacing-5: 20px;
$spacing-6: 24px;
$spacing-8: 32px;
$spacing-10: 40px;
$spacing-12: 48px;
$spacing-16: 64px;
$spacing-20: 80px;
$spacing-24: 96px;
$spacing-32: 128px;

// 字体系统
$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-2xl: 20px;
$font-size-3xl: 24px;
$font-size-4xl: 30px;
$font-size-5xl: 36px;

$line-height-tight: 1.25;
$line-height-snug: 1.375;
$line-height-normal: 1.5;
$line-height-relaxed: 1.625;
$line-height-loose: 2;

$font-weight-thin: 100;
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
$font-weight-extrabold: 800;
$font-weight-black: 900;

// 圆角系统
$radius-xs: 2px;
$radius-sm: 4px;
$radius-md: 6px;
$radius-lg: 8px;
$radius-xl: 12px;
$radius-2xl: 16px;
$radius-3xl: 20px;
$radius-full: 9999px;

// 阴影系统
$shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
$shadow-2: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
$shadow-3: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 2px 8px -1px rgba(0, 0, 0, 0.05), 0 4px 4px 0 rgba(0, 0, 0, 0.03);
$shadow-4: 0 2px 4px 0 rgba(0, 0, 0, 0.03), 0 4px 8px -2px rgba(0, 0, 0, 0.05), 0 6px 12px 0 rgba(0, 0, 0, 0.05);
$shadow-5: 0 4px 6px 0 rgba(0, 0, 0, 0.05), 0 10px 20px -2px rgba(0, 0, 0, 0.04), 0 8px 16px 0 rgba(0, 0, 0, 0.06);
$shadow-6: 0 8px 12px 0 rgba(0, 0, 0, 0.05), 0 16px 24px -4px rgba(0, 0, 0, 0.04), 0 12px 20px 0 rgba(0, 0, 0, 0.08);

// 暗色主题阴影
$dark-shadow-1: 0 1px 2px 0 rgba(0, 0, 0, 0.16), 0 1px 6px -1px rgba(0, 0, 0, 0.12), 0 2px 4px 0 rgba(0, 0, 0, 0.09);
$dark-shadow-2: 0 1px 2px 0 rgba(0, 0, 0, 0.16), 0 1px 6px -1px rgba(0, 0, 0, 0.12), 0 2px 4px 0 rgba(0, 0, 0, 0.09);
$dark-shadow-3: 0 1px 2px 0 rgba(0, 0, 0, 0.16), 0 2px 8px -1px rgba(0, 0, 0, 0.20), 0 4px 4px 0 rgba(0, 0, 0, 0.12);
$dark-shadow-4: 0 2px 4px 0 rgba(0, 0, 0, 0.16), 0 4px 8px -2px rgba(0, 0, 0, 0.20), 0 6px 12px 0 rgba(0, 0, 0, 0.20);
$dark-shadow-5: 0 4px 6px 0 rgba(0, 0, 0, 0.20), 0 10px 20px -2px rgba(0, 0, 0, 0.16), 0 8px 16px 0 rgba(0, 0, 0, 0.24);
$dark-shadow-6: 0 8px 12px 0 rgba(0, 0, 0, 0.20), 0 16px 24px -4px rgba(0, 0, 0, 0.16), 0 12px 20px 0 rgba(0, 0, 0, 0.32);

// ==================== 动画系统 ====================

// 过渡时间
$transition-fast: 0.1s;
$transition-base: 0.2s;
$transition-slow: 0.3s;
$transition-slower: 0.5s;

// 缓动函数
$ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
$ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
$ease-out-back: cubic-bezier(0.34, 1.56, 0.64, 1);
$ease-in-out-circ: cubic-bezier(0.85, 0, 0.15, 1);

// ==================== 层级系统 ====================

$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;
$z-toast: 1080;

// ==================== 响应式断点 ====================

$breakpoint-xs: 375px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-2xl: 1400px;

// ==================== 字体系统 ====================

$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
$font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

// ==================== 组件特定变量 ====================

// 导航栏
$navbar-height: 60px;
$navbar-padding: $spacing-4;

// 按钮
$btn-padding-sm: $spacing-2 $spacing-4;
$btn-padding-base: $spacing-3 $spacing-5;
$btn-padding-lg: $spacing-4 $spacing-6;

// 输入框
$input-padding: $spacing-3 $spacing-4;
$input-border-width: 1px;
$input-focus-ring: 0 0 0 3px;

// 卡片
$card-padding: $spacing-5;
$card-border-radius: $radius-2xl;

// ==================== 性能优化变量 ====================

// GPU加速
$gpu-hack: translateZ(0);
$gpu-3d: translate3d(0, 0, 0);

// 内容可见性
$content-visibility-auto: auto;
$contain-layout: layout;
$contain-style: style;
$contain-paint: paint;
$contain-size: size;
