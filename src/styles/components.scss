// 组件样式库 - 可复用的组件样式
// 提供标准化的组件样式，确保一致性

// ==================== 按钮组件 ====================

.btn {
  @include btn-base;
  @include btn-size(base);
  
  // 按钮变体
  &--primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-inverse);
    
    &:hover {
      background: var(--primary-hover);
      border-color: var(--primary-hover);
      @include shadow(3);
    }
    
    &:active {
      background: var(--primary-active);
      border-color: var(--primary-active);
    }
  }
  
  &--secondary {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--text-inverse);
    
    &:hover {
      background: var(--secondary-hover);
      border-color: var(--secondary-hover);
      @include shadow(3);
    }
    
    &:active {
      background: var(--secondary-active);
      border-color: var(--secondary-active);
    }
  }
  
  &--outline {
    background: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
    
    &:hover {
      background: var(--primary-color);
      color: var(--text-inverse);
      @include shadow(3);
    }
  }
  
  &--ghost {
    background: transparent;
    border-color: var(--border-color);
    color: var(--text-primary);
    
    &:hover {
      background: var(--bg-secondary);
      border-color: var(--border-color-dark);
    }
  }
  
  &--text {
    background: transparent;
    border-color: transparent;
    color: var(--primary-color);
    padding: $spacing-2 $spacing-3;
    
    &:hover {
      background: var(--bg-secondary);
      color: var(--primary-hover);
    }
  }
  
  // 按钮尺寸
  &--xs { @include btn-size(xs); }
  &--sm { @include btn-size(sm); }
  &--lg { @include btn-size(lg); }
  &--xl { @include btn-size(xl); }
  
  // 按钮形状
  &--square { border-radius: $radius-lg; }
  &--round { border-radius: $radius-full; }
  &--block { width: 100%; }
}

// ==================== 输入框组件 ====================

.input {
  @include input-base;
  
  &--sm {
    padding: $spacing-2 $spacing-3;
    font-size: $font-size-sm;
    border-radius: $radius-lg;
  }
  
  &--lg {
    padding: $spacing-4 $spacing-5;
    font-size: $font-size-lg;
    border-radius: $radius-2xl;
  }
  
  &--error {
    border-color: var(--error-color);
    
    &:focus {
      box-shadow: $input-focus-ring rgba(var(--error-color), 0.1);
    }
  }
  
  &--success {
    border-color: var(--success-color);
    
    &:focus {
      box-shadow: $input-focus-ring rgba(var(--success-color), 0.1);
    }
  }
}

// 输入框组
.input-group {
  display: flex;
  align-items: stretch;
  
  .input {
    border-radius: 0;
    
    &:first-child {
      border-top-left-radius: $radius-xl;
      border-bottom-left-radius: $radius-xl;
    }
    
    &:last-child {
      border-top-right-radius: $radius-xl;
      border-bottom-right-radius: $radius-xl;
    }
    
    &:not(:last-child) {
      border-right: none;
    }
  }
  
  &__text {
    display: flex;
    align-items: center;
    padding: $spacing-3 $spacing-4;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-size: $font-size-base;
    
    &:first-child {
      border-top-left-radius: $radius-xl;
      border-bottom-left-radius: $radius-xl;
      border-right: none;
    }
    
    &:last-child {
      border-top-right-radius: $radius-xl;
      border-bottom-right-radius: $radius-xl;
      border-left: none;
    }
  }
}

// 文本域
.textarea {
  @include input-base;
  min-height: 80px;
  resize: vertical;
  line-height: $line-height-relaxed;
}

// 选择框
.select {
  @include input-base;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right $spacing-3 center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: $spacing-10;
  
  &:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%230052D9' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }
}

// ==================== 卡片组件 ====================

.card {
  @include card-base;
  
  &--flat {
    box-shadow: none;
    border: 1px solid var(--border-color);
  }
  
  &--elevated {
    @include shadow(4);
    
    &:hover {
      @include shadow(5);
      transform: translateY(-2px);
    }
  }
  
  &--interactive {
    cursor: pointer;
    @include transition(all);
    
    &:hover {
      @include shadow(4);
      transform: translateY(-2px);
    }
    
    &:active {
      transform: translateY(0);
      @include shadow(2);
    }
  }
  
  &__header {
    padding: $spacing-5 $spacing-5 $spacing-3;
    border-bottom: 1px solid var(--divider-color);
  }
  
  &__body {
    padding: $spacing-5;
  }
  
  &__footer {
    padding: $spacing-3 $spacing-5 $spacing-5;
    border-top: 1px solid var(--divider-color);
    background: var(--bg-secondary);
  }
}

// ==================== 导航栏组件 ====================

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  @include glass-effect();
  border-bottom: 1px solid var(--divider-color);
  height: $navbar-height;
  @include transition(all);
  
  &__content {
    @include flex-between;
    padding: 0 $navbar-padding;
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  &__brand {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: var(--text-primary);
    text-decoration: none;
  }
  
  &__nav {
    @include flex-center;
    gap: $spacing-6;
    
    a {
      color: var(--text-secondary);
      text-decoration: none;
      @include transition(color);
      
      &:hover {
        color: var(--text-primary);
      }
      
      &.active {
        color: var(--primary-color);
      }
    }
  }
  
  &__actions {
    @include flex-center;
    gap: $spacing-3;
  }
}

// ==================== 模态框组件 ====================

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  @include flex-center;
  
  &__backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-mask);
    z-index: var(--z-modal-backdrop);
  }
  
  &__content {
    position: relative;
    z-index: var(--z-modal);
    background: var(--bg-primary);
    border-radius: $radius-2xl;
    @include shadow(6);
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
  }
  
  &__header {
    padding: $spacing-6 $spacing-6 $spacing-4;
    border-bottom: 1px solid var(--divider-color);
    
    h2 {
      margin: 0;
      font-size: $font-size-xl;
      font-weight: $font-weight-semibold;
      color: var(--text-primary);
    }
  }
  
  &__body {
    padding: $spacing-6;
  }
  
  &__footer {
    padding: $spacing-4 $spacing-6 $spacing-6;
    border-top: 1px solid var(--divider-color);
    @include flex-between;
    gap: $spacing-3;
  }
}

// ==================== 加载组件 ====================

.loading {
  @include flex-center;
  
  &__spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color-light);
    border-top: 4px solid var(--primary-color);
    border-radius: $radius-full;
    animation: spin 1s linear infinite;
  }
  
  &__text {
    margin-top: $spacing-3;
    color: var(--text-secondary);
    font-size: $font-size-sm;
  }
  
  // 尺寸变体
  &--sm .loading__spinner {
    width: 24px;
    height: 24px;
    border-width: 2px;
  }
  
  &--lg .loading__spinner {
    width: 60px;
    height: 60px;
    border-width: 6px;
  }
}

// ==================== 标签组件 ====================

.tag {
  display: inline-flex;
  align-items: center;
  gap: $spacing-1;
  padding: $spacing-1 $spacing-3;
  border-radius: $radius-full;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  line-height: $line-height-tight;
  
  &--primary {
    background: rgba(var(--primary-color), 0.1);
    color: var(--primary-color);
  }
  
  &--success {
    background: rgba(var(--success-color), 0.1);
    color: var(--success-color);
  }
  
  &--warning {
    background: rgba(var(--warning-color), 0.1);
    color: var(--warning-color);
  }
  
  &--error {
    background: rgba(var(--error-color), 0.1);
    color: var(--error-color);
  }
  
  &--neutral {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
  }
}
