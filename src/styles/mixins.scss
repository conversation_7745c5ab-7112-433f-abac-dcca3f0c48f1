// SCSS混入库 - 可复用的样式模式
// 这个文件会被自动导入到所有SCSS文件中

// 导入变量
@use 'variables' as *;

// ==================== 响应式混入 ====================

// 媒体查询混入
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) { @content; }
  }
  @else if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) { @content; }
  }
  @else if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) { @content; }
  }
  @else if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) { @content; }
  }
  @else if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) { @content; }
  }
  @else if $breakpoint == 2xl {
    @media (min-width: #{$breakpoint-2xl}) { @content; }
  }
}

// 移动端优先响应式
@mixin mobile-first($size) {
  @media (min-width: $size) { @content; }
}

// 桌面端优先响应式
@mixin desktop-first($size) {
  @media (max-width: $size - 1px) { @content; }
}

// ==================== 布局混入 ====================

// Flexbox居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Flexbox两端对齐
@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// Flexbox列布局
@mixin flex-column {
  display: flex;
  flex-direction: column;
}

// 绝对居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 固定宽高比
@mixin aspect-ratio($width, $height) {
  position: relative;
  
  &::before {
    content: '';
    display: block;
    padding-top: percentage($height / $width);
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// ==================== 文本混入 ====================

// 文本截断
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 文本渐变
@mixin text-gradient($gradient) {
  background: $gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// 字体平滑
@mixin font-smooth {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// ==================== 视觉效果混入 ====================

// 阴影系统
@mixin shadow($level: 2) {
  @if $level == 1 { box-shadow: $shadow-1; }
  @else if $level == 2 { box-shadow: $shadow-2; }
  @else if $level == 3 { box-shadow: $shadow-3; }
  @else if $level == 4 { box-shadow: $shadow-4; }
  @else if $level == 5 { box-shadow: $shadow-5; }
  @else if $level == 6 { box-shadow: $shadow-6; }
}

// 暗色主题阴影
@mixin dark-shadow($level: 2) {
  [data-theme="dark"] & {
    @if $level == 1 { box-shadow: $dark-shadow-1; }
    @else if $level == 2 { box-shadow: $dark-shadow-2; }
    @else if $level == 3 { box-shadow: $dark-shadow-3; }
    @else if $level == 4 { box-shadow: $dark-shadow-4; }
    @else if $level == 5 { box-shadow: $dark-shadow-5; }
    @else if $level == 6 { box-shadow: $dark-shadow-6; }
  }
}

// 毛玻璃效果
@mixin glass-effect($opacity: 0.95, $blur: 10px) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur($blur);
  -webkit-backdrop-filter: blur($blur);
  
  [data-theme="dark"] & {
    background: rgba(20, 20, 20, $opacity);
  }
}

// 渐变背景
@mixin gradient-bg($direction: 135deg, $colors...) {
  background: linear-gradient($direction, $colors);
}

// ==================== 动画混入 ====================

// 过渡动画
@mixin transition($properties: all, $duration: $transition-base, $easing: ease) {
  transition: $properties $duration $easing;
}

// 悬停效果
@mixin hover-lift($distance: 2px, $shadow-level: 4) {
  transition: transform $transition-base, box-shadow $transition-base;
  
  &:hover {
    transform: translateY(-$distance);
    @include shadow($shadow-level);
  }
}

// 点击效果
@mixin active-scale($scale: 0.98) {
  transition: transform $transition-fast;
  
  &:active {
    transform: scale($scale);
  }
}

// GPU加速
@mixin gpu-accelerated {
  transform: $gpu-hack;
  will-change: transform;
}

// ==================== 性能优化混入 ====================

// 内容可见性优化
@mixin content-visibility($size: 200px) {
  content-visibility: $content-visibility-auto;
  contain-intrinsic-size: $size;
}

// 包含优化
@mixin contain($types: layout style paint) {
  contain: $types;
}

// 懒加载动画
@mixin lazy-load-animation {
  opacity: 0;
  transform: translateY($spacing-5);
  transition: opacity $transition-base ease, transform $transition-base ease;
  
  &.in-view {
    opacity: 1;
    transform: translateY(0);
  }
}

// ==================== 组件混入 ====================

// 按钮基础样式
@mixin btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-2;
  border: 1px solid transparent;
  border-radius: $radius-2xl;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  line-height: $line-height-tight;
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  @include transition();
  position: relative;
  overflow: hidden;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
  
  @include active-scale();
}

// 按钮尺寸
@mixin btn-size($size: base) {
  @if $size == xs {
    padding: $spacing-1 $spacing-3;
    font-size: $font-size-xs;
    border-radius: $radius-lg;
  }
  @else if $size == sm {
    padding: $spacing-2 $spacing-4;
    font-size: $font-size-sm;
    border-radius: $radius-xl;
  }
  @else if $size == base {
    padding: $spacing-3 $spacing-5;
    font-size: $font-size-base;
    border-radius: $radius-2xl;
  }
  @else if $size == lg {
    padding: $spacing-4 $spacing-6;
    font-size: $font-size-lg;
    border-radius: $radius-3xl;
  }
  @else if $size == xl {
    padding: $spacing-5 $spacing-8;
    font-size: $font-size-xl;
    border-radius: $radius-3xl;
  }
}

// 输入框基础样式
@mixin input-base {
  width: 100%;
  padding: $input-padding;
  border: $input-border-width solid var(--border-color);
  border-radius: $radius-xl;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  background: var(--bg-primary);
  color: var(--text-primary);
  @include transition();
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: $input-focus-ring rgba(var(--primary-color), 0.1);
  }
  
  &:hover:not(:focus) {
    border-color: var(--border-color-dark);
  }
  
  &::placeholder {
    color: var(--text-placeholder);
  }
  
  &:disabled {
    background: var(--bg-disabled);
    color: var(--text-disabled);
    cursor: not-allowed;
    opacity: 0.6;
  }
}

// 卡片基础样式
@mixin card-base {
  background: var(--bg-primary);
  border-radius: $card-border-radius;
  @include shadow(2);
  border: 1px solid var(--border-color-light);
  overflow: hidden;
  @include transition();
  
  &:hover {
    @include shadow(3);
    transform: translateY(-1px);
  }
}

// ==================== 工具混入 ====================

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 隐藏滚动条
@mixin hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 自定义滚动条
@mixin custom-scrollbar($width: 6px, $track-color: var(--bg-secondary), $thumb-color: var(--border-color)) {
  &::-webkit-scrollbar {
    width: $width;
    height: $width;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: $radius-sm;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: $radius-sm;
    @include transition(background);
    
    &:hover {
      background: var(--text-tertiary);
    }
  }
}

// 安全区域适配
@mixin safe-area($position: top) {
  @if $position == top {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
  }
  @else if $position == bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
  @else if $position == left {
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
  }
  @else if $position == right {
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right);
  }
}
