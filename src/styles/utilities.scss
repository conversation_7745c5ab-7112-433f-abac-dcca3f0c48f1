// CSS工具类 - 原子化样式类
// 提供常用的原子化样式，减少重复代码

// 导入依赖
@use 'variables' as *;
@use 'mixins' as *;

// ==================== 布局工具类 ====================

// Flexbox
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

// Flex对齐
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.items-baseline { align-items: baseline; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

// Flex组合类
.flex-center { @include flex-center; }
.flex-between { @include flex-between; }
.flex-column { @include flex-column; }

// Flex grow/shrink
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

// Grid
.grid { display: grid; }
.inline-grid { display: inline-grid; }

// Position
.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

// ==================== 间距工具类 ====================

// Margin
.m-0 { margin: $spacing-0; }
.m-1 { margin: $spacing-1; }
.m-2 { margin: $spacing-2; }
.m-3 { margin: $spacing-3; }
.m-4 { margin: $spacing-4; }
.m-5 { margin: $spacing-5; }
.m-6 { margin: $spacing-6; }
.m-8 { margin: $spacing-8; }
.m-10 { margin: $spacing-10; }
.m-12 { margin: $spacing-12; }
.m-16 { margin: $spacing-16; }
.m-20 { margin: $spacing-20; }
.m-24 { margin: $spacing-24; }
.m-32 { margin: $spacing-32; }

// Margin X轴
.mx-0 { margin-left: $spacing-0; margin-right: $spacing-0; }
.mx-1 { margin-left: $spacing-1; margin-right: $spacing-1; }
.mx-2 { margin-left: $spacing-2; margin-right: $spacing-2; }
.mx-3 { margin-left: $spacing-3; margin-right: $spacing-3; }
.mx-4 { margin-left: $spacing-4; margin-right: $spacing-4; }
.mx-5 { margin-left: $spacing-5; margin-right: $spacing-5; }
.mx-6 { margin-left: $spacing-6; margin-right: $spacing-6; }
.mx-8 { margin-left: $spacing-8; margin-right: $spacing-8; }
.mx-auto { margin-left: auto; margin-right: auto; }

// Margin Y轴
.my-0 { margin-top: $spacing-0; margin-bottom: $spacing-0; }
.my-1 { margin-top: $spacing-1; margin-bottom: $spacing-1; }
.my-2 { margin-top: $spacing-2; margin-bottom: $spacing-2; }
.my-3 { margin-top: $spacing-3; margin-bottom: $spacing-3; }
.my-4 { margin-top: $spacing-4; margin-bottom: $spacing-4; }
.my-5 { margin-top: $spacing-5; margin-bottom: $spacing-5; }
.my-6 { margin-top: $spacing-6; margin-bottom: $spacing-6; }
.my-8 { margin-top: $spacing-8; margin-bottom: $spacing-8; }

// Padding
.p-0 { padding: $spacing-0; }
.p-1 { padding: $spacing-1; }
.p-2 { padding: $spacing-2; }
.p-3 { padding: $spacing-3; }
.p-4 { padding: $spacing-4; }
.p-5 { padding: $spacing-5; }
.p-6 { padding: $spacing-6; }
.p-8 { padding: $spacing-8; }
.p-10 { padding: $spacing-10; }
.p-12 { padding: $spacing-12; }
.p-16 { padding: $spacing-16; }
.p-20 { padding: $spacing-20; }
.p-24 { padding: $spacing-24; }
.p-32 { padding: $spacing-32; }

// Padding X轴
.px-0 { padding-left: $spacing-0; padding-right: $spacing-0; }
.px-1 { padding-left: $spacing-1; padding-right: $spacing-1; }
.px-2 { padding-left: $spacing-2; padding-right: $spacing-2; }
.px-3 { padding-left: $spacing-3; padding-right: $spacing-3; }
.px-4 { padding-left: $spacing-4; padding-right: $spacing-4; }
.px-5 { padding-left: $spacing-5; padding-right: $spacing-5; }
.px-6 { padding-left: $spacing-6; padding-right: $spacing-6; }
.px-8 { padding-left: $spacing-8; padding-right: $spacing-8; }

// Padding Y轴
.py-0 { padding-top: $spacing-0; padding-bottom: $spacing-0; }
.py-1 { padding-top: $spacing-1; padding-bottom: $spacing-1; }
.py-2 { padding-top: $spacing-2; padding-bottom: $spacing-2; }
.py-3 { padding-top: $spacing-3; padding-bottom: $spacing-3; }
.py-4 { padding-top: $spacing-4; padding-bottom: $spacing-4; }
.py-5 { padding-top: $spacing-5; padding-bottom: $spacing-5; }
.py-6 { padding-top: $spacing-6; padding-bottom: $spacing-6; }
.py-8 { padding-top: $spacing-8; padding-bottom: $spacing-8; }

// ==================== 文本工具类 ====================

// 文本对齐
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// 文本大小
.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $font-size-sm; }
.text-base { font-size: $font-size-base; }
.text-lg { font-size: $font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-2xl { font-size: $font-size-2xl; }
.text-3xl { font-size: $font-size-3xl; }
.text-4xl { font-size: $font-size-4xl; }
.text-5xl { font-size: $font-size-5xl; }

// 字体粗细
.font-thin { font-weight: $font-weight-thin; }
.font-light { font-weight: $font-weight-light; }
.font-normal { font-weight: $font-weight-normal; }
.font-medium { font-weight: $font-weight-medium; }
.font-semibold { font-weight: $font-weight-semibold; }
.font-bold { font-weight: $font-weight-bold; }
.font-extrabold { font-weight: $font-weight-extrabold; }
.font-black { font-weight: $font-weight-black; }

// 行高
.leading-tight { line-height: $line-height-tight; }
.leading-snug { line-height: $line-height-snug; }
.leading-normal { line-height: $line-height-normal; }
.leading-relaxed { line-height: $line-height-relaxed; }
.leading-loose { line-height: $line-height-loose; }

// 文本截断
.truncate { @include text-ellipsis(1); }
.line-clamp-2 { @include text-ellipsis(2); }
.line-clamp-3 { @include text-ellipsis(3); }
.line-clamp-4 { @include text-ellipsis(4); }

// ==================== 颜色工具类 ====================

// 文本颜色
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-quaternary { color: var(--text-quaternary); }
.text-placeholder { color: var(--text-placeholder); }
.text-disabled { color: var(--text-disabled); }
.text-inverse { color: var(--text-inverse); }

// 主题色文本
.text-brand { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

// 背景颜色
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }
.bg-quaternary { background-color: var(--bg-quaternary); }

// 主题色背景
.bg-brand { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-info { background-color: var(--info-color); }

// ==================== 边框工具类 ====================

// 边框宽度
.border-0 { border-width: 0; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-4 { border-width: 4px; }

// 边框颜色
.border-primary { border-color: var(--border-color); }
.border-light { border-color: var(--border-color-light); }
.border-dark { border-color: var(--border-color-dark); }
.border-brand { border-color: var(--primary-color); }

// 圆角
.rounded-none { border-radius: 0; }
.rounded-xs { border-radius: $radius-xs; }
.rounded-sm { border-radius: $radius-sm; }
.rounded { border-radius: $radius-md; }
.rounded-md { border-radius: $radius-md; }
.rounded-lg { border-radius: $radius-lg; }
.rounded-xl { border-radius: $radius-xl; }
.rounded-2xl { border-radius: $radius-2xl; }
.rounded-3xl { border-radius: $radius-3xl; }
.rounded-full { border-radius: $radius-full; }

// ==================== 阴影工具类 ====================

.shadow-none { box-shadow: none; }
.shadow-1 { @include shadow(1); }
.shadow-2 { @include shadow(2); }
.shadow-3 { @include shadow(3); }
.shadow-4 { @include shadow(4); }
.shadow-5 { @include shadow(5); }
.shadow-6 { @include shadow(6); }

// ==================== 显示工具类 ====================

.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

// 可见性
.visible { visibility: visible; }
.invisible { visibility: hidden; }

// 透明度
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

// ==================== 尺寸工具类 ====================

// 宽度
.w-auto { width: auto; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-fit { width: fit-content; }

// 高度
.h-auto { height: auto; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-fit { height: fit-content; }

// 最小/最大尺寸
.min-w-0 { min-width: 0; }
.min-w-full { min-width: 100%; }
.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }

.max-w-none { max-width: none; }
.max-w-full { max-width: 100%; }
.max-h-full { max-height: 100%; }
.max-h-screen { max-height: 100vh; }

// ==================== 溢出工具类 ====================

.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }

.overflow-x-auto { overflow-x: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-scroll { overflow-x: scroll; }

.overflow-y-auto { overflow-y: auto; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-scroll { overflow-y: scroll; }

// ==================== 交互工具类 ====================

// 指针事件
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

// 用户选择
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }
.select-auto { user-select: auto; }

// 光标
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }

// ==================== 性能优化工具类 ====================

// GPU加速
.gpu-accelerated { @include gpu-accelerated; }

// 内容可见性
.content-visibility { @include content-visibility; }

// 包含优化
.contain-layout { contain: layout; }
.contain-style { contain: style; }
.contain-paint { contain: paint; }
.contain-size { contain: size; }
.contain-strict { contain: strict; }

// 懒加载
.lazy-load { @include lazy-load-animation; }

// ==================== 动画工具类 ====================

// 过渡
.transition-none { transition: none; }
.transition-all { @include transition(all); }
.transition-colors { @include transition(color, background-color, border-color); }
.transition-opacity { @include transition(opacity); }
.transition-shadow { @include transition(box-shadow); }
.transition-transform { @include transition(transform); }

// 变换
.transform { transform: translateZ(0); }
.transform-none { transform: none; }

// 悬停效果
.hover-lift { @include hover-lift; }
.hover-scale { 
  @include transition(transform);
  &:hover { transform: scale(1.02); }
}

// 点击效果
.active-scale { @include active-scale; }
