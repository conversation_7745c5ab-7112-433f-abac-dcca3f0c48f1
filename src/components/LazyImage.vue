<template>
  <view class="lazy-image-container" :style="containerStyle">
    <image 
      v-if="loaded || error"
      :src="loaded ? currentSrc : errorSrc"
      :mode="mode"
      :class="['lazy-image', { 'loaded': loaded, 'error': error }]"
      :style="imageStyle"
      @load="onLoad"
      @error="onError" />
    
    <!-- 加载占位符 -->
    <view v-if="!loaded && !error" class="loading-placeholder" :style="imageStyle">
      <view class="loading-content">
        <view class="loading-spinner" v-if="showSpinner"></view>
        <image v-if="placeholder" :src="placeholder" class="placeholder-image" :mode="mode" />
        <view v-else class="default-placeholder">
          <text class="placeholder-icon">🖼️</text>
        </view>
      </view>
    </view>
    
    <!-- 错误占位符 -->
    <view v-if="error" class="error-placeholder" :style="imageStyle" @tap="retry">
      <view class="error-content">
        <text class="error-icon">❌</text>
        <text class="error-text">加载失败，点击重试</text>
      </view>
    </view>
  </view>
</template>

<script>
import { imageCache } from '@/utils/performance'

export default {
  name: 'LazyImage',
  props: {
    src: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      default: ''
    },
    errorSrc: {
      type: String,
      default: ''
    },
    mode: {
      type: String,
      default: 'aspectFill'
    },
    width: {
      type: [String, Number],
      default: '100%'
    },
    height: {
      type: [String, Number],
      default: 'auto'
    },
    borderRadius: {
      type: [String, Number],
      default: 0
    },
    lazy: {
      type: Boolean,
      default: true
    },
    cache: {
      type: Boolean,
      default: true
    },
    showSpinner: {
      type: Boolean,
      default: true
    },
    retryCount: {
      type: Number,
      default: 3
    }
  },
  data() {
    return {
      loaded: false,
      error: false,
      currentSrc: '',
      observer: null,
      retries: 0,
      loadStartTime: 0
    }
  },
  computed: {
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? this.width + 'px' : this.width,
        height: typeof this.height === 'number' ? this.height + 'px' : this.height,
        borderRadius: typeof this.borderRadius === 'number' ? this.borderRadius + 'px' : this.borderRadius
      }
    },
    imageStyle() {
      return {
        width: '100%',
        height: '100%',
        borderRadius: typeof this.borderRadius === 'number' ? this.borderRadius + 'px' : this.borderRadius
      }
    }
  },
  mounted() {
    if (this.lazy) {
      this.initLazyLoad()
    } else {
      this.loadImage()
    }
  },
  beforeUnmount() {
    if (this.observer) {
      this.observer.unobserve(this.$el)
    }
  },
  methods: {
    initLazyLoad() {
      if ('IntersectionObserver' in window) {
        this.observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.loadImage()
              this.observer.unobserve(entry.target)
            }
          })
        }, {
          rootMargin: '50px'
        })
        
        this.observer.observe(this.$el)
      } else {
        // 不支持IntersectionObserver时直接加载
        this.loadImage()
      }
    },
    
    async loadImage() {
      if (!this.src || this.loaded) return
      
      this.loadStartTime = Date.now()
      
      try {
        let imageSrc = this.src
        
        // 如果启用缓存，尝试从缓存获取
        if (this.cache) {
          imageSrc = await imageCache.get(this.src)
        }
        
        // 预加载图片以确保能正常显示
        await this.preloadImage(imageSrc)
        
        this.currentSrc = imageSrc
        this.loaded = true
        this.error = false
        
        // 记录加载时间
        const loadTime = Date.now() - this.loadStartTime
        this.$emit('load', { src: this.src, loadTime })
        
      } catch (error) {
        console.error('Image load failed:', error)
        this.handleLoadError()
      }
    },
    
    preloadImage(src) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.onload = resolve
        img.onerror = reject
        img.src = src
      })
    },
    
    handleLoadError() {
      this.retries++
      
      if (this.retries <= this.retryCount) {
        // 延迟重试
        setTimeout(() => {
          this.loadImage()
        }, Math.pow(2, this.retries) * 1000) // 指数退避
      } else {
        this.error = true
        this.loaded = false
        this.$emit('error', { src: this.src, retries: this.retries })
      }
    },
    
    onLoad() {
      const loadTime = Date.now() - this.loadStartTime
      this.$emit('load', { src: this.src, loadTime })
    },
    
    onError() {
      this.handleLoadError()
    },
    
    retry() {
      this.error = false
      this.retries = 0
      this.loadImage()
    }
  }
}
</script>

<style lang="scss" scoped>
.lazy-image-container {
  position: relative;
  overflow: hidden;
  background: var(--bg-secondary);
}

.lazy-image {
  transition: opacity 0.3s ease;
  opacity: 0;
  
  &.loaded {
    opacity: 1;
  }
  
  &.error {
    opacity: 0.5;
  }
}

.loading-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  
  .loading-content {
    text-align: center;
    
    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid var(--border-color);
      border-top: 2px solid var(--primary-color);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 10px;
    }
    
    .placeholder-image {
      max-width: 80%;
      max-height: 80%;
      opacity: 0.6;
    }
    
    .default-placeholder {
      .placeholder-icon {
        font-size: 24px;
        opacity: 0.4;
      }
    }
  }
}

.error-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  cursor: pointer;
  
  .error-content {
    text-align: center;
    
    .error-icon {
      font-size: 24px;
      display: block;
      margin-bottom: 5px;
      opacity: 0.6;
    }
    
    .error-text {
      font-size: 12px;
      color: var(--text-secondary);
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>