<template>
  <view class="theme-toggle" @tap="toggleTheme">
    <view class="toggle-container" :class="{ 'dark': isDark }">
      <view class="toggle-track">
        <view class="toggle-thumb" :class="{ 'active': isDark }">
          <view class="theme-icon">
            <text v-if="currentTheme === 'auto'">🌓</text>
            <text v-else-if="isDark">🌙</text>
            <text v-else>☀️</text>
          </view>
        </view>
      </view>
      
      <view class="theme-label" v-if="showLabel">
        <text class="label-text">{{ themeLabel }}</text>
      </view>
    </view>
    
    <!-- 主题选择器（长按显示） -->
    <view class="theme-selector" v-if="showSelector" @tap.stop>
      <view class="selector-header">
        <text class="selector-title">选择主题</text>
      </view>
      
      <view class="theme-options">
        <view 
          class="theme-option" 
          v-for="option in themeOptions" 
          :key="option.value"
          :class="{ 'active': currentTheme === option.value }"
          @tap="selectTheme(option.value)">
          <view class="option-icon">
            <text>{{ option.icon }}</text>
          </view>
          <view class="option-content">
            <text class="option-name">{{ option.name }}</text>
            <text class="option-desc">{{ option.description }}</text>
          </view>
          <view class="option-check" v-if="currentTheme === option.value">
            <text>✓</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 遮罩层 -->
    <view 
      class="theme-mask" 
      v-if="showSelector" 
      @tap="hideSelector">
    </view>
  </view>
</template>

<script>
import { themeManager } from '@/utils/theme'

export default {
  name: 'ThemeToggle',
  props: {
    showLabel: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'medium', // small, medium, large
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    variant: {
      type: String,
      default: 'switch', // switch, button, icon
      validator: value => ['switch', 'button', 'icon'].includes(value)
    }
  },
  data() {
    return {
      currentTheme: 'auto',
      effectiveTheme: 'light',
      showSelector: false,
      longPressTimer: null,
      themeOptions: [
        {
          value: 'light',
          name: '浅色模式',
          description: '经典的浅色界面',
          icon: '☀️'
        },
        {
          value: 'dark',
          name: '深色模式',
          description: '护眼的深色界面',
          icon: '🌙'
        },
        {
          value: 'auto',
          name: '跟随系统',
          description: '根据系统设置自动切换',
          icon: '🌓'
        }
      ]
    }
  },
  computed: {
    isDark() {
      return this.effectiveTheme === 'dark'
    },
    themeLabel() {
      const option = this.themeOptions.find(opt => opt.value === this.currentTheme)
      return option ? option.name : '主题'
    },
    toggleClass() {
      return [
        'theme-toggle',
        `theme-toggle--${this.variant}`,
        `theme-toggle--${this.size}`,
        {
          'theme-toggle--dark': this.isDark,
          'theme-toggle--with-label': this.showLabel
        }
      ]
    }
  },
  mounted() {
    this.updateThemeState()
    
    // 监听主题变化
    this.removeThemeListener = themeManager.addListener(this.onThemeChange)
  },
  beforeUnmount() {
    if (this.removeThemeListener) {
      this.removeThemeListener()
    }
    
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer)
    }
  },
  methods: {
    updateThemeState() {
      this.currentTheme = themeManager.getCurrentTheme()
      this.effectiveTheme = themeManager.getTheme()
    },
    
    onThemeChange(themeInfo) {
      this.currentTheme = themeInfo.current
      this.effectiveTheme = themeInfo.effective
    },
    
    toggleTheme() {
      if (this.variant === 'switch') {
        // 开关模式：在light和dark之间切换
        const newTheme = this.effectiveTheme === 'dark' ? 'light' : 'dark'
        themeManager.setTheme(newTheme)
      } else {
        // 其他模式：循环切换所有主题
        themeManager.toggleTheme()
      }
      
      this.$emit('change', {
        current: this.currentTheme,
        effective: this.effectiveTheme
      })
      
      // 触觉反馈
      this.hapticFeedback()
    },
    
    selectTheme(theme) {
      themeManager.setTheme(theme)
      this.hideSelector()
      
      this.$emit('change', {
        current: theme,
        effective: themeManager.getTheme()
      })
      
      this.hapticFeedback()
    },
    
    showThemeSelector() {
      this.showSelector = true
      this.$emit('selector-show')
    },
    
    hideSelector() {
      this.showSelector = false
      this.$emit('selector-hide')
    },
    
    onLongPress() {
      this.showThemeSelector()
      this.hapticFeedback('heavy')
    },
    
    onTouchStart() {
      this.longPressTimer = setTimeout(() => {
        this.onLongPress()
      }, 500)
    },
    
    onTouchEnd() {
      if (this.longPressTimer) {
        clearTimeout(this.longPressTimer)
        this.longPressTimer = null
      }
    },
    
    hapticFeedback(type = 'light') {
      if (typeof uni !== 'undefined' && uni.vibrateShort) {
        try {
          if (type === 'heavy') {
            uni.vibrateLong()
          } else {
            uni.vibrateShort()
          }
        } catch (error) {
          // 某些平台可能不支持触觉反馈
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.theme-toggle {
  position: relative;
  @include flex-center;
  user-select: none;
}

.toggle-container {
  @include flex-center;
  gap: $spacing-2;
  cursor: pointer;
  @include transition(all);
  @include active-scale(0.95);

  &:hover {
    opacity: 0.8;
  }
}

.toggle-track {
  position: relative;
  width: 48px;
  height: 24px;
  background: var(--border-color);
  border-radius: $radius-full;
  @include transition(all);

  .dark & {
    background: var(--primary-color);
  }
}

.toggle-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: var(--bg-primary);
  border-radius: $radius-full;
  @include transition(all);
  @include flex-center;
  @include shadow(2);

  &.active {
    transform: translateX(24px);
  }
}

.theme-icon {
  font-size: 12px;
  line-height: 1;
  @include transition(color);
}

.theme-label {
  .label-text {
    font-size: $font-size-sm;
    color: var(--text-secondary);
    font-weight: $font-weight-medium;
  }
}

// ==================== 主题选择器 ====================

.theme-selector {
  position: fixed;
  bottom: $spacing-6;
  left: $spacing-4;
  right: $spacing-4;
  background: var(--bg-primary);
  border-radius: $radius-2xl;
  @include shadow(6);
  z-index: var(--z-modal);
  animation: slideUp $transition-base ease-out;
  border: 1px solid var(--border-color-light);
  @include contain(layout style);
}

.selector-header {
  padding: $spacing-5 $spacing-5 $spacing-3;
  border-bottom: 1px solid var(--divider-color);

  .selector-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: var(--text-primary);
    margin: 0;
  }
}

.theme-options {
  padding: $spacing-3;
}

.theme-option {
  display: flex;
  align-items: center;
  padding: var(--spacing-4);
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition-base);
  
  &:hover {
    background: var(--bg-secondary);
  }
  
  &.active {
    background: var(--primary-color);
    color: var(--text-inverse);
    
    .option-desc {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.option-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  margin-right: var(--spacing-3);
  font-size: 16px;
  
  .theme-option.active & {
    background: rgba(255, 255, 255, 0.2);
  }
}

.option-content {
  flex: 1;
  
  .option-name {
    display: block;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-1);
  }
  
  .option-desc {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
  }
}

.option-check {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
}

.theme-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-mask);
  z-index: calc(var(--z-modal) - 1);
  animation: fadeIn var(--transition-base) ease-out;
}

// 尺寸变体
.theme-toggle--small {
  .toggle-track {
    width: 36px;
    height: 18px;
  }
  
  .toggle-thumb {
    width: 14px;
    height: 14px;
    
    &.active {
      transform: translateX(18px);
    }
  }
  
  .theme-icon {
    font-size: 10px;
  }
}

.theme-toggle--large {
  .toggle-track {
    width: 60px;
    height: 30px;
  }
  
  .toggle-thumb {
    width: 26px;
    height: 26px;
    
    &.active {
      transform: translateX(30px);
    }
  }
  
  .theme-icon {
    font-size: 14px;
  }
}

// 按钮变体
.theme-toggle--button {
  .toggle-container {
    padding: var(--spacing-2) var(--spacing-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
  }
}

// 图标变体
.theme-toggle--icon {
  .toggle-track {
    display: none;
  }
  
  .toggle-container {
    width: 40px;
    height: 40px;
    background: var(--bg-secondary);
    border-radius: var(--radius-full);
    border: 1px solid var(--border-color);
    justify-content: center;
  }
  
  .theme-icon {
    font-size: 18px;
  }
}
</style>
