<template>
  <view class="dressup-result">
    <!-- 结果预览区域 -->
    <view class="result-preview">
      <image 
        class="result-image" 
        :src="resultImage" 
        mode="aspectFit"
        @tap="previewFullscreen" />
      
      <!-- 对比模式切换 -->
      <view class="compare-toggle" v-if="originalImage">
        <button 
          class="compare-btn" 
          :class="{ 'active': showComparison }"
          @tap="toggleComparison">
          {{ showComparison ? '隐藏对比' : '显示对比' }}
        </button>
      </view>
      
      <!-- 对比视图 -->
      <view class="comparison-view" v-if="showComparison && originalImage">
        <view class="comparison-container">
          <view class="comparison-item">
            <image class="comparison-image" :src="originalImage" mode="aspectFit" />
            <text class="comparison-label">原图</text>
          </view>
          <view class="comparison-divider"></view>
          <view class="comparison-item">
            <image class="comparison-image" :src="resultImage" mode="aspectFit" />
            <text class="comparison-label">换装后</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮组 -->
    <view class="action-buttons">
      <button class="action-btn secondary" @tap="retryDressup">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">重新换装</text>
      </button>
      
      <button class="action-btn secondary" @tap="applyFilter">
        <text class="btn-icon">✨</text>
        <text class="btn-text">美化</text>
      </button>
      
      <button class="action-btn primary" @tap="saveResult">
        <text class="btn-icon">💾</text>
        <text class="btn-text">保存</text>
      </button>
      
      <button class="action-btn secondary" @tap="shareResult">
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享</text>
      </button>
    </view>
    
    <!-- 滤镜选择器 -->
    <view class="filter-selector" v-if="showFilters">
      <view class="filter-header">
        <text class="filter-title">选择滤镜</text>
        <text class="filter-close" @tap="hideFilters">✕</text>
      </view>
      <scroll-view class="filter-list" scroll-x>
        <view 
          class="filter-item" 
          v-for="filter in filters" 
          :key="filter.id"
          :class="{ 'active': selectedFilter === filter.id }"
          @tap="selectFilter(filter)">
          <view class="filter-preview">
            <image class="filter-thumb" :src="resultImage" mode="aspectFill" />
            <view class="filter-overlay" :style="{ filter: filter.css }"></view>
          </view>
          <text class="filter-name">{{ filter.name }}</text>
        </view>
      </scroll-view>
      <view class="filter-actions">
        <button class="filter-btn cancel" @tap="hideFilters">取消</button>
        <button class="filter-btn confirm" @tap="applySelectedFilter">应用</button>
      </view>
    </view>
    
    <!-- 保存选项弹窗 -->
    <view class="save-modal" v-if="showSaveModal" @tap="hideSaveModal">
      <view class="save-content" @tap.stop>
        <view class="save-header">
          <text class="save-title">保存选项</text>
        </view>
        <view class="save-options">
          <view class="save-option" @tap="saveToAlbum">
            <view class="option-icon">📱</view>
            <view class="option-info">
              <text class="option-title">保存到相册</text>
              <text class="option-desc">保存到手机相册</text>
            </view>
          </view>
          <view class="save-option" @tap="saveToCloud">
            <view class="option-icon">☁️</view>
            <view class="option-info">
              <text class="option-title">保存到云端</text>
              <text class="option-desc">同步到个人作品库</text>
            </view>
          </view>
          <view class="save-option" @tap="saveWithWatermark">
            <view class="option-icon">🏷️</view>
            <view class="option-info">
              <text class="option-title">添加水印保存</text>
              <text class="option-desc">带应用标识保存</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 分享选项弹窗 -->
    <view class="share-modal" v-if="showShareModal" @tap="hideShareModal">
      <view class="share-content" @tap.stop>
        <view class="share-header">
          <text class="share-title">分享到</text>
        </view>
        <view class="share-platforms">
          <view class="platform-item" @tap="shareToWechat">
            <view class="platform-icon wechat">💬</view>
            <text class="platform-name">微信</text>
          </view>
          <view class="platform-item" @tap="shareToWeibo">
            <view class="platform-icon weibo">📝</view>
            <text class="platform-name">微博</text>
          </view>
          <view class="platform-item" @tap="shareToQQ">
            <view class="platform-icon qq">🐧</view>
            <text class="platform-name">QQ</text>
          </view>
          <view class="platform-item" @tap="copyLink">
            <view class="platform-icon copy">🔗</view>
            <text class="platform-name">复制链接</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { saveImageToPhotosAlbum, previewImage, applyImageFilter } from '@/utils/imageUtils'
import { worksAPI } from '@/api/index'

export default {
  name: 'DressupResult',
  props: {
    resultImage: {
      type: String,
      required: true
    },
    originalImage: {
      type: String,
      default: ''
    },
    clothesInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showComparison: false,
      showFilters: false,
      showSaveModal: false,
      showShareModal: false,
      selectedFilter: null,
      processedImage: '',
      filters: [
        { id: 'none', name: '原图', css: 'none' },
        { id: 'vintage', name: '复古', css: 'sepia(0.5) contrast(1.2)' },
        { id: 'bright', name: '明亮', css: 'brightness(1.2) contrast(1.1)' },
        { id: 'soft', name: '柔和', css: 'blur(0.5px) brightness(1.1)' },
        { id: 'cool', name: '冷色调', css: 'hue-rotate(180deg) saturate(1.2)' },
        { id: 'warm', name: '暖色调', css: 'hue-rotate(-30deg) saturate(1.3)' },
        { id: 'bw', name: '黑白', css: 'grayscale(1) contrast(1.2)' },
        { id: 'dramatic', name: '戏剧化', css: 'contrast(1.5) saturate(1.4)' }
      ]
    }
  },
  mounted() {
    this.processedImage = this.resultImage
  },
  methods: {
    previewFullscreen() {
      previewImage([this.processedImage])
    },
    
    toggleComparison() {
      this.showComparison = !this.showComparison
    },
    
    retryDressup() {
      this.$emit('retry')
    },
    
    applyFilter() {
      this.showFilters = true
    },
    
    hideFilters() {
      this.showFilters = false
      this.selectedFilter = null
    },
    
    selectFilter(filter) {
      this.selectedFilter = filter.id
    },
    
    async applySelectedFilter() {
      if (!this.selectedFilter) return
      
      const filter = this.filters.find(f => f.id === this.selectedFilter)
      if (!filter) return
      
      uni.showLoading({ title: '应用滤镜中...' })
      
      try {
        if (filter.id === 'none') {
          this.processedImage = this.resultImage
        } else {
          // 这里应用滤镜效果
          this.processedImage = await applyImageFilter(this.resultImage, filter.id)
        }
        
        this.hideFilters()
        uni.showToast({
          title: '滤镜应用成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('应用滤镜失败:', error)
        uni.showToast({
          title: '滤镜应用失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },
    
    saveResult() {
      this.showSaveModal = true
    },
    
    hideSaveModal() {
      this.showSaveModal = false
    },
    
    async saveToAlbum() {
      try {
        await saveImageToPhotosAlbum(this.processedImage)
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
        this.hideSaveModal()
      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败，请检查权限',
          icon: 'none'
        })
      }
    },
    
    async saveToCloud() {
      try {
        uni.showLoading({ title: '上传中...' })
        
        const workData = {
          image: this.processedImage,
          original_image: this.originalImage,
          clothes_info: this.clothesInfo,
          created_at: new Date().toISOString()
        }
        
        await worksAPI.saveWork(workData)
        
        uni.showToast({
          title: '保存到云端成功',
          icon: 'success'
        })
        this.hideSaveModal()
      } catch (error) {
        console.error('云端保存失败:', error)
        uni.showToast({
          title: '云端保存失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },
    
    async saveWithWatermark() {
      try {
        uni.showLoading({ title: '添加水印中...' })
        
        // 这里添加水印逻辑
        const watermarkedImage = await this.addWatermark(this.processedImage)
        await saveImageToPhotosAlbum(watermarkedImage)
        
        uni.showToast({
          title: '带水印保存成功',
          icon: 'success'
        })
        this.hideSaveModal()
      } catch (error) {
        console.error('水印保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },
    
    addWatermark(imagePath) {
      return new Promise((resolve) => {
        // 模拟添加水印
        setTimeout(() => {
          resolve(imagePath)
        }, 1000)
      })
    },
    
    shareResult() {
      this.showShareModal = true
    },
    
    hideShareModal() {
      this.showShareModal = false
    },
    
    shareToWechat() {
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 2,
        imageUrl: this.processedImage,
        success: () => {
          uni.showToast({ title: '分享成功', icon: 'success' })
        },
        fail: () => {
          uni.showToast({ title: '分享失败', icon: 'none' })
        }
      })
      this.hideShareModal()
    },
    
    shareToWeibo() {
      // 微博分享逻辑
      uni.showToast({ title: '微博分享功能开发中', icon: 'none' })
      this.hideShareModal()
    },
    
    shareToQQ() {
      // QQ分享逻辑
      uni.showToast({ title: 'QQ分享功能开发中', icon: 'none' })
      this.hideShareModal()
    },
    
    copyLink() {
      uni.setClipboardData({
        data: this.processedImage,
        success: () => {
          uni.showToast({ title: '链接已复制', icon: 'success' })
        }
      })
      this.hideShareModal()
    }
  }
}
</script>

<style lang="scss" scoped>
.dressup-result {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.result-preview {
  flex: 1;
  position: relative;
  background: var(--bg-primary);
  border-radius: var(--radius-large);
  margin: 20px;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
  
  .result-image {
    width: 100%;
    height: 100%;
  }
  
  .compare-toggle {
    position: absolute;
    top: 15px;
    right: 15px;
    
    .compare-btn {
      background: rgba(0, 0, 0, 0.6);
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      backdrop-filter: blur(10px);
      
      &.active {
        background: var(--primary-color);
      }
    }
  }
  
  .comparison-view {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 15px;
    
    .comparison-container {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .comparison-item {
        flex: 1;
        text-align: center;
        
        .comparison-image {
          width: 100%;
          height: 120px;
          border-radius: var(--radius-medium);
        }
        
        .comparison-label {
          display: block;
          margin-top: 8px;
          font-size: 12px;
          color: var(--text-secondary);
        }
      }
      
      .comparison-divider {
        width: 2px;
        height: 60px;
        background: var(--divider-color);
        border-radius: 1px;
      }
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background: var(--bg-primary);
  border-top: 1px solid var(--divider-color);
  
  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 12px 16px;
    border: none;
    border-radius: var(--radius-medium);
    min-width: 70px;
    
    &.primary {
      background: var(--primary-color);
      color: white;
    }
    
    &.secondary {
      background: var(--bg-secondary);
      color: var(--text-primary);
    }
    
    .btn-icon {
      font-size: 20px;
    }
    
    .btn-text {
      font-size: 12px;
    }
  }
}

.filter-selector {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border-radius: 20px 20px 0 0;
  box-shadow: var(--shadow-heavy);
  z-index: 1000;
  animation: slideUp 0.3s ease;
  
  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--divider-color);
    
    .filter-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .filter-close {
      font-size: 20px;
      color: var(--text-secondary);
      padding: 5px;
    }
  }
  
  .filter-list {
    padding: 15px 20px;
    white-space: nowrap;
    
    .filter-item {
      display: inline-block;
      margin-right: 15px;
      text-align: center;
      
      &.active .filter-preview {
        border-color: var(--primary-color);
      }
      
      .filter-preview {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-medium);
        border: 2px solid transparent;
        overflow: hidden;
        position: relative;
        margin-bottom: 8px;
        
        .filter-thumb {
          width: 100%;
          height: 100%;
        }
        
        .filter-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
        }
      }
      
      .filter-name {
        font-size: 12px;
        color: var(--text-secondary);
      }
    }
  }
  
  .filter-actions {
    display: flex;
    gap: 15px;
    padding: 20px;
    border-top: 1px solid var(--divider-color);
    
    .filter-btn {
      flex: 1;
      padding: 12px;
      border: none;
      border-radius: var(--radius-medium);
      font-size: 16px;
      
      &.cancel {
        background: var(--bg-secondary);
        color: var(--text-primary);
      }
      
      &.confirm {
        background: var(--primary-color);
        color: white;
      }
    }
  }
}

.save-modal, .share-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: flex-end;
  
  .save-content, .share-content {
    width: 100%;
    background: var(--bg-primary);
    border-radius: 20px 20px 0 0;
    padding: 20px;
    animation: slideUp 0.3s ease;
    
    .save-header, .share-header {
      text-align: center;
      margin-bottom: 20px;
      
      .save-title, .share-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
      }
    }
    
    .save-options {
      .save-option {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid var(--divider-color);
        
        &:last-child {
          border-bottom: none;
        }
        
        .option-icon {
          font-size: 24px;
          margin-right: 15px;
          width: 40px;
          text-align: center;
        }
        
        .option-info {
          flex: 1;
          
          .option-title {
            font-size: 16px;
            color: var(--text-primary);
            display: block;
            margin-bottom: 2px;
          }
          
          .option-desc {
            font-size: 12px;
            color: var(--text-secondary);
          }
        }
      }
    }
    
    .share-platforms {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;
      
      .platform-item {
        text-align: center;
        
        .platform-icon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          margin: 0 auto 8px;
          
          &.wechat { background: #07C160; }
          &.weibo { background: #E6162D; }
          &.qq { background: #12B7F5; }
          &.copy { background: var(--bg-secondary); }
        }
        
        .platform-name {
          font-size: 12px;
          color: var(--text-secondary);
        }
      }
    }
  }
}
</style>