<template>
  <view class="virtual-list" :style="containerStyle" @scroll="onScroll">
    <view class="virtual-list-phantom" :style="{ height: totalHeight + 'px' }"></view>
    <view class="virtual-list-content" :style="{ transform: contentTransform }">
      <view 
        v-for="item in visibleData" 
        :key="getItemKey(item)"
        class="virtual-list-item"
        :style="{ height: itemHeight + 'px' }">
        <slot :item="item.data" :index="item.index"></slot>
      </view>
    </view>
  </view>
</template>

<script>
import { VirtualScrollManager } from '@/utils/performance'

export default {
  name: 'VirtualList',
  props: {
    data: {
      type: Array,
      required: true
    },
    itemHeight: {
      type: Number,
      default: 100
    },
    height: {
      type: [String, Number],
      default: '100%'
    },
    buffer: {
      type: Number,
      default: 5
    },
    keyField: {
      type: String,
      default: 'id'
    }
  },
  data() {
    return {
      scrollTop: 0,
      virtualManager: null
    }
  },
  computed: {
    containerStyle() {
      return {
        height: typeof this.height === 'number' ? this.height + 'px' : this.height,
        overflow: 'auto'
      }
    },
    containerHeight() {
      return typeof this.height === 'number' ? this.height : 500
    },
    totalHeight() {
      return this.virtualManager ? this.virtualManager.getTotalHeight() : 0
    },
    visibleData() {
      return this.virtualManager ? this.virtualManager.getVisibleData() : []
    },
    contentTransform() {
      return this.virtualManager ? this.virtualManager.getTransform() : 'translateY(0px)'
    }
  },
  watch: {
    data: {
      handler(newData) {
        if (this.virtualManager) {
          this.virtualManager.setData(newData)
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initVirtualManager()
  },
  methods: {
    initVirtualManager() {
      this.virtualManager = new VirtualScrollManager({
        itemHeight: this.itemHeight,
        containerHeight: this.containerHeight,
        buffer: this.buffer
      })
      
      if (this.data) {
        this.virtualManager.setData(this.data)
      }
    },
    
    onScroll(e) {
      const scrollTop = e.detail.scrollTop
      this.scrollTop = scrollTop
      
      if (this.virtualManager) {
        this.virtualManager.updateScrollTop(scrollTop)
      }
      
      this.$emit('scroll', e)
    },
    
    getItemKey(item) {
      return item.data[this.keyField] || item.index
    },
    
    scrollToIndex(index) {
      const scrollTop = index * this.itemHeight
      this.$nextTick(() => {
        uni.pageScrollTo({
          scrollTop,
          duration: 300
        })
      })
    },
    
    scrollToTop() {
      this.scrollToIndex(0)
    },
    
    scrollToBottom() {
      this.scrollToIndex(this.data.length - 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.virtual-list {
  position: relative;
  
  .virtual-list-phantom {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: -1;
  }
  
  .virtual-list-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }
  
  .virtual-list-item {
    overflow: hidden;
  }
}
</style>