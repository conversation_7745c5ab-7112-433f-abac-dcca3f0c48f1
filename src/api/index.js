// API服务配置和请求封装

// 基础配置
const BASE_URL = 'https://api.ai-dressup.com' // 实际项目中替换为真实API地址
const TIMEOUT = 30000

/**
 * 封装uni.request
 * @param {object} options 请求选项
 * @returns {Promise} 请求Promise
 */
function request(options) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': getToken(),
        ...options.header
      },
      timeout: TIMEOUT,
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === 0) {
            resolve(res.data.data)
          } else {
            uni.showToast({
              title: res.data.message || '请求失败',
              icon: 'none'
            })
            reject(new Error(res.data.message))
          }
        } else {
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          })
          reject(new Error('网络请求失败'))
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '网络连接失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

/**
 * 获取存储的token
 * @returns {string} token
 */
function getToken() {
  return uni.getStorageSync('token') || ''
}

/**
 * 设置token
 * @param {string} token 
 */
export function setToken(token) {
  uni.setStorageSync('token', token)
}

/**
 * 清除token
 */
export function clearToken() {
  uni.removeStorageSync('token')
}

// 用户相关API
export const userAPI = {
  // 用户登录
  login(data) {
    return request({
      url: '/user/login',
      method: 'POST',
      data
    })
  },
  
  // 获取用户信息
  getUserInfo() {
    return request({
      url: '/user/info'
    })
  },
  
  // 更新用户信息
  updateUserInfo(data) {
    return request({
      url: '/user/update',
      method: 'PUT',
      data
    })
  },
  
  // 上传头像
  uploadAvatar(filePath) {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: BASE_URL + '/user/avatar',
        filePath,
        name: 'avatar',
        header: {
          'Authorization': getToken()
        },
        success: (res) => {
          const data = JSON.parse(res.data)
          if (data.code === 0) {
            resolve(data.data)
          } else {
            reject(new Error(data.message))
          }
        },
        fail: reject
      })
    })
  }
}

// 服装相关API
export const clothesAPI = {
  // 获取服装列表
  getClothes(params = {}) {
    return request({
      url: '/clothes/list',
      data: params
    })
  },
  
  // 获取服装详情
  getClothesDetail(id) {
    return request({
      url: `/clothes/${id}`
    })
  },
  
  // 搜索服装
  searchClothes(keyword, filters = {}) {
    return request({
      url: '/clothes/search',
      data: {
        keyword,
        ...filters
      }
    })
  },
  
  // 获取服装分类
  getCategories() {
    return request({
      url: '/clothes/categories'
    })
  },
  
  // 获取热门推荐
  getRecommendations(limit = 10) {
    return request({
      url: '/clothes/recommendations',
      data: { limit }
    })
  }
}

// AI换装相关API
export const aiAPI = {
  // 人体姿态检测
  detectPose(imageData) {
    return request({
      url: '/ai/detect-pose',
      method: 'POST',
      data: { image: imageData },
      header: {
        'Content-Type': 'application/json'
      }
    })
  },
  
  // 虚拟试衣
  virtualTryOn(personImage, clothesId, poseData) {
    return request({
      url: '/ai/virtual-tryon',
      method: 'POST',
      data: {
        person_image: personImage,
        clothes_id: clothesId,
        pose_data: poseData
      }
    })
  },
  
  // 图像分割
  segmentImage(imageData) {
    return request({
      url: '/ai/segment',
      method: 'POST',
      data: { image: imageData }
    })
  },
  
  // 风格迁移
  styleTransfer(imageData, styleType) {
    return request({
      url: '/ai/style-transfer',
      method: 'POST',
      data: {
        image: imageData,
        style: styleType
      }
    })
  }
}

// 收藏相关API
export const favoriteAPI = {
  // 获取收藏列表
  getFavorites(type = 'clothes') {
    return request({
      url: '/favorites',
      data: { type }
    })
  },
  
  // 添加收藏
  addFavorite(itemId, type = 'clothes') {
    return request({
      url: '/favorites',
      method: 'POST',
      data: { item_id: itemId, type }
    })
  },
  
  // 取消收藏
  removeFavorite(itemId, type = 'clothes') {
    return request({
      url: `/favorites/${itemId}`,
      method: 'DELETE',
      data: { type }
    })
  },
  
  // 检查是否已收藏
  checkFavorite(itemId, type = 'clothes') {
    return request({
      url: `/favorites/check/${itemId}`,
      data: { type }
    })
  }
}

// 作品相关API
export const worksAPI = {
  // 获取我的作品
  getMyWorks(page = 1, limit = 20) {
    return request({
      url: '/works/my',
      data: { page, limit }
    })
  },
  
  // 保存作品
  saveWork(data) {
    return request({
      url: '/works',
      method: 'POST',
      data
    })
  },
  
  // 删除作品
  deleteWork(id) {
    return request({
      url: `/works/${id}`,
      method: 'DELETE'
    })
  },
  
  // 分享作品
  shareWork(id, platform) {
    return request({
      url: `/works/${id}/share`,
      method: 'POST',
      data: { platform }
    })
  },
  
  // 上传作品图片
  uploadWorkImage(filePath) {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: BASE_URL + '/works/upload',
        filePath,
        name: 'image',
        header: {
          'Authorization': getToken()
        },
        success: (res) => {
          const data = JSON.parse(res.data)
          if (data.code === 0) {
            resolve(data.data)
          } else {
            reject(new Error(data.message))
          }
        },
        fail: reject
      })
    })
  }
}

// 通用上传API
export const uploadAPI = {
  // 上传图片
  uploadImage(filePath, type = 'common') {
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: BASE_URL + '/upload/image',
        filePath,
        name: 'file',
        formData: { type },
        header: {
          'Authorization': getToken()
        },
        success: (res) => {
          const data = JSON.parse(res.data)
          if (data.code === 0) {
            resolve(data.data)
          } else {
            reject(new Error(data.message))
          }
        },
        fail: reject
      })
    })
  }
}

export default {
  userAPI,
  clothesAPI,
  aiAPI,
  favoriteAPI,
  worksAPI,
  uploadAPI
}