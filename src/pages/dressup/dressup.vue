<template>
  <view class="dressup-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="nav-left" @tap="goBack">
          <text class="back-icon">←</text>
        </view>
        <text class="nav-title">虚拟换装</text>
        <view class="nav-right">
          <text class="save-btn" @tap="saveResult">保存</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 换装结果展示 -->
      <view class="result-section" v-if="showResult">
        <DressupResult 
          :result-image="resultImage"
          :original-image="originalImage"
          :clothes-info="currentClothesInfo"
          @retry="retryDressup" />
      </view>
      
      <!-- 照片预览区域 -->
      <view class="photo-preview-section" v-else>
        <view class="preview-container" v-if="selectedPhoto">
          <image class="preview-image" :src="selectedPhoto" mode="aspectFit" />
          <view class="processing-overlay" v-if="isProcessing">
            <view class="loading-animation">
              <view class="loading-circle"></view>
              <text class="loading-text">AI正在为您换装...</text>
              <view class="loading-progress">
                <view class="progress-bar">
                  <view class="progress-fill" :style="{ width: processingProgress + '%' }"></view>
                </view>
                <text class="progress-text">{{ processingProgress }}%</text>
              </view>
            </view>
          </view>
        </view>
        <view class="empty-preview" v-else>
          <view class="empty-icon">📷</view>
          <text class="empty-text">请选择照片开始换装</text>
          <view class="upload-actions">
            <button class="upload-btn primary" @tap="takePhoto">拍照</button>
            <button class="upload-btn secondary" @tap="choosePhoto">相册</button>
          </view>
        </view>
      </view>

      <!-- 服装选择抽屉 -->
      <view class="clothes-drawer" :class="{ 'drawer-expanded': drawerExpanded }">
        <view class="drawer-handle" @tap="toggleDrawer">
          <view class="handle-bar"></view>
          <text class="drawer-title">选择服装</text>
        </view>
        
        <view class="drawer-content">
          <!-- 分类标签 -->
          <scroll-view class="category-tabs" scroll-x>
            <view class="tab-item" 
                  v-for="category in clothesCategories" 
                  :key="category.id"
                  :class="{ 'active': activeCategory === category.id }"
                  @tap="switchCategory(category.id)">
              <text class="tab-text">{{ category.name }}</text>
            </view>
          </scroll-view>
          
          <!-- 服装网格 -->
          <scroll-view class="clothes-grid" scroll-y>
            <view class="grid-container">
              <view class="clothes-item" 
                    v-for="item in currentClothes" 
                    :key="item.id"
                    :class="{ 'selected': selectedClothes === item.id }"
                    @tap="selectClothes(item)">
                <image class="clothes-image" :src="item.image" mode="aspectFill" />
                <view class="clothes-info">
                  <text class="clothes-name">{{ item.name }}</text>
                  <text class="clothes-price">¥{{ item.price }}</text>
                </view>
                <view class="select-indicator" v-if="selectedClothes === item.id">✓</view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import DressupResult from '@/components/DressupResult.vue'

export default {
  components: {
    DressupResult
  },
  data() {
    return {
      selectedPhoto: '',
      selectedClothes: null,
      isProcessing: false,
      drawerExpanded: false,
      activeCategory: 1,
      showResult: false,
      resultImage: '',
      originalImage: '',
      currentClothesInfo: {},
      processingProgress: 0,
      clothesCategories: [
        { id: 1, name: '上衣' },
        { id: 2, name: '裤装' },
        { id: 3, name: '裙装' },
        { id: 4, name: '外套' }
      ],
      clothesData: {
        1: [ // 上衣
          { id: 101, name: '白色衬衫', price: 199, image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300' },
          { id: 102, name: '条纹T恤', price: 89, image: 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=300' },
          { id: 103, name: '针织毛衣', price: 299, image: 'https://images.unsplash.com/photo-1434389677669-e08b4cac3105?w=300' }
        ],
        2: [ // 裤装
          { id: 201, name: '牛仔裤', price: 199, image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=300' },
          { id: 202, name: '休闲裤', price: 159, image: 'https://images.unsplash.com/photo-1473966968600-fa801b869a1a?w=300' }
        ],
        3: [ // 裙装
          { id: 301, name: '连衣裙', price: 299, image: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=300' },
          { id: 302, name: '半身裙', price: 159, image: 'https://images.unsplash.com/photo-1583496661160-fb5886a13d44?w=300' }
        ],
        4: [ // 外套
          { id: 401, name: '西装外套', price: 599, image: 'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?w=300' },
          { id: 402, name: '牛仔外套', price: 299, image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=300' }
        ]
      }
    }
  },
  computed: {
    currentClothes() {
      return this.clothesData[this.activeCategory] || []
    }
  },
  onLoad(options) {
    if (options.type === 'camera') {
      this.takePhoto()
    } else if (options.type === 'album') {
      this.choosePhoto()
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    takePhoto() {
      uni.chooseImage({
        count: 1,
        sourceType: ['camera'],
        success: (res) => {
          this.selectedPhoto = res.tempFilePaths[0]
          this.drawerExpanded = true
        }
      })
    },
    choosePhoto() {
      uni.chooseImage({
        count: 1,
        sourceType: ['album'],
        success: (res) => {
          this.selectedPhoto = res.tempFilePaths[0]
          this.drawerExpanded = true
        }
      })
    },
    toggleDrawer() {
      this.drawerExpanded = !this.drawerExpanded
    },
    switchCategory(categoryId) {
      this.activeCategory = categoryId
    },
    selectClothes(item) {
      this.selectedClothes = item.id
      this.currentClothesInfo = item
      this.processAIDressup(item)
    },
    processAIDressup(clothesItem) {
      if (!this.selectedPhoto) return
      
      this.isProcessing = true
      this.processingProgress = 0
      
      // 模拟AI处理进度
      const progressInterval = setInterval(() => {
        this.processingProgress += Math.random() * 15
        if (this.processingProgress >= 100) {
          this.processingProgress = 100
          clearInterval(progressInterval)
        }
      }, 200)
      
      // 模拟AI处理过程
      setTimeout(() => {
        this.isProcessing = false
        this.processingProgress = 100
        clearInterval(progressInterval)
        
        // 设置结果数据
        this.originalImage = this.selectedPhoto
        this.resultImage = this.selectedPhoto // 实际应用中这里是AI处理后的图片
        this.showResult = true
        
        uni.showToast({
          title: '换装完成！',
          icon: 'success'
        })
      }, 3000)
    },
    retryDressup() {
      this.showResult = false
      this.resultImage = ''
      this.originalImage = ''
      this.selectedClothes = null
      this.currentClothesInfo = {}
    },
    saveResult() {
      if (!this.selectedPhoto) {
        uni.showToast({
          title: '请先选择照片',
          icon: 'none'
        })
        return
      }
      
      uni.showToast({
        title: '保存成功！',
        icon: 'success'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dressup-container {
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  
  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 44px 20px 12px;
    
    .nav-left {
      width: 60px;
      
      .back-icon {
        font-size: 24px;
        color: #0052D9;
      }
    }
    
    .nav-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
    
    .nav-right {
      width: 60px;
      text-align: right;
      
      .save-btn {
        font-size: 16px;
        color: #0052D9;
      }
    }
  }
}

.main-content {
  flex: 1;
  padding-top: 100px;
  display: flex;
  flex-direction: column;
}

.photo-preview-section {
  flex: 1;
  padding: 20px;
  
  .preview-container {
    width: 100%;
    height: 100%;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    
    .preview-image {
      width: 100%;
      height: 100%;
    }
    
    .processing-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      
      .loading-animation {
        text-align: center;
        color: white;
        
        .loading-circle {
          width: 60px;
          height: 60px;
          border: 4px solid rgba(255, 255, 255, 0.3);
          border-top: 4px solid #0052D9;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 15px;
        }
        
        .loading-text {
          font-size: 16px;
          margin-bottom: 15px;
        }
        
        .loading-progress {
          width: 200px;
          
          .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 8px;
            
            .progress-fill {
              height: 100%;
              background: #0052D9;
              border-radius: 2px;
              transition: width 0.3s ease;
            }
          }
          
          .progress-text {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
          }
        }
      }
    }
  }
}

.clothes-drawer {
  background: white;
  border-radius: 20px 20px 0 0;
  transition: all 0.3s ease;
  height: 300px;
  
  &.drawer-expanded {
    height: 60vh;
  }
  
  .drawer-handle {
    padding: 15px 20px;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    
    .handle-bar {
      width: 40px;
      height: 4px;
      background: #ddd;
      border-radius: 2px;
      margin: 0 auto 10px;
    }
    
    .drawer-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  
  .drawer-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .category-tabs {
      padding: 15px 20px;
      white-space: nowrap;
      
      .tab-item {
        display: inline-block;
        padding: 8px 16px;
        margin-right: 10px;
        border-radius: 20px;
        background: #f5f5f5;
        
        &.active {
          background: #0052D9;
          
          .tab-text {
            color: white;
          }
        }
        
        .tab-text {
          font-size: 14px;
          color: #666;
        }
      }
    }
    
    .clothes-grid {
      flex: 1;
      
      .grid-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 0 20px 20px;
        
        .clothes-item {
          background: white;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          position: relative;
          
          &.selected {
            border: 2px solid #0052D9;
          }
          
          .clothes-image {
            width: 100%;
            height: 120px;
          }
          
          .clothes-info {
            padding: 10px;
            
            .clothes-name {
              font-size: 14px;
              color: #333;
              display: block;
              margin-bottom: 5px;
            }
            
            .clothes-price {
              font-size: 16px;
              font-weight: 600;
              color: #FF8A00;
            }
          }
          
          .select-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background: #0052D9;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
          }
        }
      }
    }
  }
}

.result-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.empty-preview {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  
  .empty-icon {
    font-size: 80px;
    margin-bottom: 20px;
    opacity: 0.5;
  }
  
  .empty-text {
    font-size: 16px;
    color: #999;
    margin-bottom: 30px;
  }
  
  .upload-actions {
    display: flex;
    gap: 15px;
    
    .upload-btn {
      padding: 12px 24px;
      border-radius: 25px;
      font-size: 16px;
      border: none;
      
      &.primary {
        background: #0052D9;
        color: white;
      }
      
      &.secondary {
        background: #f0f0f0;
        color: #333;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>