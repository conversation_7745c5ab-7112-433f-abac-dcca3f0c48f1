<template>
  <view class="wardrobe-page">
    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <input 
          v-model="searchKeyword" 
          placeholder="搜索服装..." 
          class="search-input"
          @input="onSearch" />
        <view class="search-icon">🔍</view>
      </view>
      
      <!-- 筛选按钮 -->
      <view class="filter-buttons">
        <view 
          v-for="filter in filters" 
          :key="filter.key"
          :class="['filter-btn', { active: activeFilters.includes(filter.key) }]"
          @tap="toggleFilter(filter.key)">
          {{ filter.label }}
        </view>
      </view>
    </view>

    <!-- 虚拟滚动服装列表 -->
    <VirtualList 
      :data="filteredClothes"
      :item-height="220"
      height="calc(100vh - 200px)"
      class="clothes-virtual-list">
      <template #default="{ item }">
        <view class="clothes-item" @tap="selectClothes(item)">
          <LazyImage 
            :src="item.image" 
            class="clothes-image"
            :placeholder="'/static/placeholder.png'"
            mode="aspectFill" />
          <view class="clothes-info">
            <text class="clothes-name">{{ item.name }}</text>
            <text class="clothes-price">¥{{ item.price }}</text>
            <text class="clothes-category">{{ getCategoryName(item.category) }}</text>
          </view>
          <view class="clothes-actions">
            <view class="action-btn favorite" @tap.stop="toggleFavorite(item)">
              {{ item.isFavorite ? '❤️' : '🤍' }}
            </view>
            <view class="action-btn try-on" @tap.stop="tryOn(item)">试穿</view>
          </view>
        </view>
      </template>
    </VirtualList>

    <!-- 加载更多 -->
    <view v-if="loading" class="loading">
      <text>加载中...</text>
    </view>

    <!-- 回到顶部 -->
    <view v-if="showBackTop" class="back-top" @tap="scrollToTop">
      ⬆️
    </view>
  </view>
</template>

<script>
import VirtualList from '@/components/VirtualList.vue'
import LazyImage from '@/components/LazyImage.vue'
import { ImageCache, debounce, DataCache } from '@/utils/performance'

export default {
  name: 'Wardrobe',
  components: {
    VirtualList,
    LazyImage
  },
  data() {
    return {
      searchKeyword: '',
      activeFilters: ['all'],
      loading: false,
      showBackTop: false,
      imageCache: null,
      dataCache: null,
      filters: [
        { key: 'all', label: '全部' },
        { key: 'tops', label: '上装' },
        { key: 'bottoms', label: '下装' },
        { key: 'dresses', label: '连衣裙' },
        { key: 'accessories', label: '配饰' }
      ],
      clothesList: []
    }
  },
  computed: {
    filteredClothes() {
      let result = this.clothesList
      
      // 筛选分类
      if (this.activeFilters.length > 0 && !this.activeFilters.includes('all')) {
        result = result.filter(item => this.activeFilters.includes(item.category))
      }
      
      // 搜索关键词
      if (this.searchKeyword) {
        result = result.filter(item => 
          item.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }
      
      return result
    }
  },
  created() {
    this.imageCache = new ImageCache()
    this.dataCache = new DataCache()
    this.onSearch = debounce(this.handleSearch, 300)
    this.loadClothes()
  },
  methods: {
    async loadClothes() {
      this.loading = true
      
      try {
        // 先尝试从缓存获取数据
        const cacheKey = 'wardrobe_clothes_list'
        let clothesData = this.dataCache.get(cacheKey)
        
        if (!clothesData) {
          // 模拟API调用
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          // 生成测试数据
          const categories = ['tops', 'bottoms', 'dresses', 'accessories']
          const clothesNames = {
            tops: ['简约白衬衫', '牛仔外套', '针织毛衣', '西装外套', '卫衣'],
            bottoms: ['休闲牛仔裤', '西装裤', '运动裤', '短裤', '阔腿裤'],
            dresses: ['黑色连衣裙', '碎花裙', '晚礼服', '休闲连衣裙', '职业套装'],
            accessories: ['丝巾', '帽子', '手表', '项链', '耳环']
          }
          
          clothesData = []
          
          for (let i = 1; i <= 100; i++) {
            const category = categories[Math.floor(Math.random() * categories.length)]
            const names = clothesNames[category]
            const name = names[Math.floor(Math.random() * names.length)]
            
            clothesData.push({
              id: i,
              name: `${name} ${i}`,
              price: Math.floor(Math.random() * 1000) + 100,
              category: category,
              image: `https://images.unsplash.com/photo-${1594633312681 + i}?w=300&h=300&fit=crop`,
              isFavorite: Math.random() > 0.8,
              brand: `品牌${Math.floor(Math.random() * 10) + 1}`,
              rating: (Math.random() * 2 + 3).toFixed(1)
            })
          }
          
          // 缓存数据，缓存30分钟
          this.dataCache.set(cacheKey, clothesData, 30 * 60 * 1000)
        }
        
        this.clothesList = clothesData
        
        // 预加载前几张图片
        this.preloadImages(clothesData.slice(0, 10))
        
      } catch (error) {
        console.error('加载服装数据失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    
    async preloadImages(items) {
      for (const item of items) {
        try {
          await this.imageCache.preload(item.image)
        } catch (error) {
          console.warn('预加载图片失败:', item.image)
        }
      }
    },
    
    handleSearch() {
      console.log('搜索:', this.searchKeyword)
      // 这里可以调用API进行搜索
      if (this.searchKeyword) {
        // 记录搜索历史
        const searchHistory = this.dataCache.get('search_history') || []
        if (!searchHistory.includes(this.searchKeyword)) {
          searchHistory.unshift(this.searchKeyword)
          // 只保留最近10次搜索
          if (searchHistory.length > 10) {
            searchHistory.pop()
          }
          this.dataCache.set('search_history', searchHistory)
        }
      }
    },
    
    toggleFilter(filterKey) {
      if (filterKey === 'all') {
        this.activeFilters = ['all']
      } else {
        const index = this.activeFilters.indexOf(filterKey)
        if (index > -1) {
          this.activeFilters.splice(index, 1)
        } else {
          this.activeFilters = this.activeFilters.filter(f => f !== 'all')
          this.activeFilters.push(filterKey)
        }
        
        if (this.activeFilters.length === 0) {
          this.activeFilters = ['all']
        }
      }
    },
    
    getCategoryName(category) {
      const categoryMap = {
        tops: '上装',
        bottoms: '下装',
        dresses: '连衣裙',
        accessories: '配饰'
      }
      return categoryMap[category] || '其他'
    },
    
    selectClothes(item) {
      console.log('选择服装:', item)
      
      // 记录浏览历史
      const viewHistory = this.dataCache.get('view_history') || []
      const existIndex = viewHistory.findIndex(h => h.id === item.id)
      
      if (existIndex > -1) {
        viewHistory.splice(existIndex, 1)
      }
      
      viewHistory.unshift({
        ...item,
        viewTime: Date.now()
      })
      
      // 只保留最近50个浏览记录
      if (viewHistory.length > 50) {
        viewHistory.pop()
      }
      
      this.dataCache.set('view_history', viewHistory)
      
      uni.navigateTo({
        url: '/pages/dressup/dressup?clothesId=' + item.id
      })
    },
    
    async toggleFavorite(item) {
      const originalState = item.isFavorite
      item.isFavorite = !item.isFavorite
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 200))
        
        // 更新本地收藏列表缓存
        const favorites = this.dataCache.get('favorites') || []
        
        if (item.isFavorite) {
          if (!favorites.find(f => f.id === item.id)) {
            favorites.push({
              ...item,
              favoriteTime: Date.now()
            })
          }
        } else {
          const index = favorites.findIndex(f => f.id === item.id)
          if (index > -1) {
            favorites.splice(index, 1)
          }
        }
        
        this.dataCache.set('favorites', favorites)
        
        uni.showToast({
          title: item.isFavorite ? '已收藏' : '已取消收藏',
          icon: 'none'
        })
        
      } catch (error) {
        // 如果失败，回滚状态
        item.isFavorite = originalState
        uni.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    },
    
    tryOn(item) {
      uni.navigateTo({
        url: '/pages/dressup/dressup?clothesId=' + item.id
      })
    },
    
    scrollToTop() {
      if (this.$refs.virtualList) {
        this.$refs.virtualList.scrollToTop()
      }
    },
    
    onScroll(e) {
      this.showBackTop = e.detail.scrollTop > 500
    }
  }
}
</script>

<style lang="scss" scoped>
.wardrobe-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  .search-section {
    position: sticky;
    top: 0;
    z-index: 100;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20rpx;
    border-bottom: 1px solid #eee;
    
    .search-bar {
      position: relative;
      margin-bottom: 20rpx;
      
      .search-input {
        width: 100%;
        height: 80rpx;
        padding: 0 100rpx 0 30rpx;
        border: 2px solid #e0e0e0;
        border-radius: 40rpx;
        font-size: 28rpx;
        background: #fff;
        transition: all 0.3s ease;
        
        &:focus {
          border-color: #007aff;
          box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
        }
      }
      
      .search-icon {
        position: absolute;
        right: 30rpx;
        top: 50%;
        transform: translateY(-50%);
        font-size: 32rpx;
        color: #999;
      }
    }
    
    .filter-buttons {
      display: flex;
      gap: 20rpx;
      overflow-x: auto;
      padding-bottom: 10rpx;
      
      .filter-btn {
        flex-shrink: 0;
        padding: 16rpx 32rpx;
        border: 2px solid #e0e0e0;
        border-radius: 50rpx;
        font-size: 26rpx;
        color: #666;
        background: #fff;
        transition: all 0.3s ease;
        
        &.active {
          background: #007aff;
          border-color: #007aff;
          color: #fff;
          transform: scale(1.05);
        }
        
        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
  
  .clothes-virtual-list {
    padding: 20rpx;
    
    .clothes-item {
      display: flex;
      background: #fff;
      border-radius: 20rpx;
      margin-bottom: 20rpx;
      padding: 20rpx;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.98);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.12);
      }
      
      .clothes-image {
        width: 160rpx;
        height: 160rpx;
        border-radius: 16rpx;
        margin-right: 20rpx;
        background: #f5f5f5;
      }
      
      .clothes-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        
        .clothes-name {
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .clothes-price {
          font-size: 28rpx;
          color: #ff4757;
          font-weight: 600;
          margin-bottom: 8rpx;
        }
        
        .clothes-category {
          font-size: 24rpx;
          color: #999;
          background: #f0f0f0;
          padding: 4rpx 12rpx;
          border-radius: 12rpx;
          align-self: flex-start;
        }
      }
      
      .clothes-actions {
        display: flex;
        flex-direction: column;
        gap: 16rpx;
        align-items: center;
        justify-content: center;
        
        .action-btn {
          padding: 12rpx 20rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          text-align: center;
          min-width: 80rpx;
          transition: all 0.3s ease;
          
          &.favorite {
            background: #ffe8e8;
            color: #ff4757;
            font-size: 28rpx;
            
            &:active {
              background: #ffcccb;
            }
          }
          
          &.try-on {
            background: #007aff;
            color: #fff;
            
            &:active {
              background: #0056cc;
            }
          }
        }
      }
    }
  }
  
  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40rpx;
    color: #999;
    font-size: 28rpx;
  }
  
  .back-top {
    position: fixed;
    right: 30rpx;
    bottom: 120rpx;
    width: 80rpx;
    height: 80rpx;
    background: rgba(0, 122, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #fff;
    box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
    z-index: 999;
    transition: all 0.3s ease;
    
    &:active {
      transform: scale(0.9);
    }
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6rpx;
  height: 6rpx;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6rpx;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
}
</style>