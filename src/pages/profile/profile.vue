<template>
  <view class="profile-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <text class="nav-title">个人中心</text>
        <view class="nav-actions">
          <text class="setting-icon" @tap="goToSettings">⚙️</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="main-content" scroll-y>
      <!-- 用户信息卡片 -->
      <view class="user-info-card">
        <view class="user-avatar-section">
          <image class="user-avatar" :src="userInfo.avatar" mode="aspectFill" @tap="changeAvatar" />
          <view class="avatar-edit-btn">
            <text class="edit-icon">📷</text>
          </view>
        </view>
        <view class="user-details">
          <text class="user-name">{{ userInfo.name }}</text>
          <text class="user-level">{{ userInfo.level }}</text>
          <view class="user-stats">
            <view class="stat-item">
              <text class="stat-number">{{ userInfo.dressupCount }}</text>
              <text class="stat-label">换装次数</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ userInfo.favoriteCount }}</text>
              <text class="stat-label">收藏数量</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{ userInfo.shareCount }}</text>
              <text class="stat-label">分享次数</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 功能菜单 -->
      <view class="menu-section">
        <view class="menu-group">
          <view class="menu-item" @tap="goToFavorites">
            <view class="menu-icon">❤️</view>
            <text class="menu-text">我的收藏</text>
            <text class="menu-arrow">></text>
          </view>
          <view class="menu-item" @tap="goToHistory">
            <view class="menu-icon">🕒</view>
            <text class="menu-text">换装历史</text>
            <text class="menu-arrow">></text>
          </view>
          <view class="menu-item" @tap="goToWorks">
            <view class="menu-icon">🎨</view>
            <text class="menu-text">我的作品</text>
            <text class="menu-arrow">></text>
          </view>
        </view>

        <view class="menu-group">
          <view class="menu-item" @tap="goToVip">
            <view class="menu-icon">👑</view>
            <text class="menu-text">会员中心</text>
            <view class="vip-badge">VIP</view>
            <text class="menu-arrow">></text>
          </view>
          <view class="menu-item" @tap="goToInvite">
            <view class="menu-icon">👥</view>
            <text class="menu-text">邀请好友</text>
            <text class="menu-arrow">></text>
          </view>
        </view>

        <view class="menu-group">
          <view class="menu-item" @tap="goToHelp">
            <view class="menu-icon">❓</view>
            <text class="menu-text">帮助中心</text>
            <text class="menu-arrow">></text>
          </view>
          <view class="menu-item" @tap="goToFeedback">
            <view class="menu-icon">💬</view>
            <text class="menu-text">意见反馈</text>
            <text class="menu-arrow">></text>
          </view>
          <view class="menu-item" @tap="goToAbout">
            <view class="menu-icon">ℹ️</view>
            <text class="menu-text">关于我们</text>
            <text class="menu-arrow">></text>
          </view>
        </view>
      </view>

      <!-- 我的作品展示 -->
      <view class="works-section">
        <view class="section-header">
          <text class="section-title">我的换装作品</text>
          <text class="more-btn" @tap="goToWorks">查看全部</text>
        </view>
        <view class="works-grid" v-if="myWorks.length > 0">
          <view class="work-item" v-for="work in myWorks.slice(0, 6)" :key="work.id" @tap="viewWork(work)">
            <image class="work-image" :src="work.image" mode="aspectFill" />
            <view class="work-overlay">
              <text class="work-date">{{ work.date }}</text>
            </view>
          </view>
        </view>
        <view class="empty-works" v-else>
          <view class="empty-icon">🎨</view>
          <text class="empty-text">还没有换装作品</text>
          <button class="create-btn" @tap="goToDressup">开始创作</button>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: {
        name: '时尚达人',
        level: 'VIP会员',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200',
        dressupCount: 128,
        favoriteCount: 45,
        shareCount: 23
      },
      myWorks: [
        {
          id: 1,
          image: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=300',
          date: '2024-01-15'
        },
        {
          id: 2,
          image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300',
          date: '2024-01-14'
        },
        {
          id: 3,
          image: 'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?w=300',
          date: '2024-01-13'
        }
      ]
    }
  },
  methods: {
    goToSettings() {
      uni.showToast({
        title: '设置功能开发中',
        icon: 'none'
      })
    },
    changeAvatar() {
      uni.chooseImage({
        count: 1,
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.userInfo.avatar = res.tempFilePaths[0]
          uni.showToast({
            title: '头像更新成功',
            icon: 'success'
          })
        }
      })
    },
    goToFavorites() {
      uni.showToast({
        title: '收藏功能开发中',
        icon: 'none'
      })
    },
    goToHistory() {
      uni.showToast({
        title: '历史记录功能开发中',
        icon: 'none'
      })
    },
    goToWorks() {
      uni.showToast({
        title: '作品管理功能开发中',
        icon: 'none'
      })
    },
    goToVip() {
      uni.showToast({
        title: 'VIP功能开发中',
        icon: 'none'
      })
    },
    goToInvite() {
      uni.showToast({
        title: '邀请功能开发中',
        icon: 'none'
      })
    },
    goToHelp() {
      uni.showToast({
        title: '帮助中心开发中',
        icon: 'none'
      })
    },
    goToFeedback() {
      uni.showToast({
        title: '反馈功能开发中',
        icon: 'none'
      })
    },
    goToAbout() {
      uni.showModal({
        title: '关于AI换装',
        content: '版本：1.0.0\n一款基于AI技术的虚拟换装应用',
        showCancel: false
      })
    },
    viewWork(work) {
      uni.previewImage({
        urls: [work.image],
        current: work.image
      })
    },
    goToDressup() {
      uni.switchTab({
        url: '/pages/dressup/dressup'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  
  .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 44px 20px 12px;
    
    .nav-title {
      font-size: 20px;
      font-weight: 700;
      color: white;
    }
    
    .nav-actions {
      .setting-icon {
        font-size: 20px;
        color: white;
      }
    }
  }
}

.main-content {
  padding-top: 100px;
  padding-bottom: 20px;
}

.user-info-card {
  margin: 20px;
  padding: 30px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  
  .user-avatar-section {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    position: relative;
    
    .user-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      border: 4px solid white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .avatar-edit-btn {
      position: absolute;
      bottom: 0;
      right: calc(50% - 50px);
      width: 28px;
      height: 28px;
      background: #0052D9;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .edit-icon {
        font-size: 14px;
        color: white;
      }
    }
  }
  
  .user-details {
    text-align: center;
    
    .user-name {
      font-size: 24px;
      font-weight: 700;
      color: #333;
      display: block;
      margin-bottom: 5px;
    }
    
    .user-level {
      font-size: 14px;
      color: #FF8A00;
      background: rgba(255, 138, 0, 0.1);
      padding: 4px 12px;
      border-radius: 12px;
      display: inline-block;
      margin-bottom: 20px;
    }
    
    .user-stats {
      display: flex;
      justify-content: space-around;
      
      .stat-item {
        text-align: center;
        
        .stat-number {
          font-size: 20px;
          font-weight: 700;
          color: #0052D9;
          display: block;
          margin-bottom: 5px;
        }
        
        .stat-label {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }
}

.menu-section {
  margin: 0 20px;
  
  .menu-group {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    margin-bottom: 15px;
    backdrop-filter: blur(20px);
    overflow: hidden;
    
    .menu-item {
      display: flex;
      align-items: center;
      padding: 18px 20px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      
      &:last-child {
        border-bottom: none;
      }
      
      .menu-icon {
        font-size: 20px;
        margin-right: 15px;
        width: 24px;
        text-align: center;
      }
      
      .menu-text {
        flex: 1;
        font-size: 16px;
        color: #333;
      }
      
      .vip-badge {
        background: linear-gradient(45deg, #FFD700, #FFA500);
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 8px;
        margin-right: 10px;
        font-weight: 600;
      }
      
      .menu-arrow {
        font-size: 16px;
        color: #ccc;
      }
    }
  }
}

.works-section {
  margin: 0 20px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }
    
    .more-btn {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  .works-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    
    .work-item {
      aspect-ratio: 1;
      border-radius: 12px;
      overflow: hidden;
      position: relative;
      
      .work-image {
        width: 100%;
        height: 100%;
      }
      
      .work-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
        padding: 15px 10px 8px;
        
        .work-date {
          font-size: 12px;
          color: white;
        }
      }
    }
  }
  
  .empty-works {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 40px 20px;
    text-align: center;
    backdrop-filter: blur(20px);
    
    .empty-icon {
      font-size: 60px;
      margin-bottom: 15px;
      opacity: 0.6;
    }
    
    .empty-text {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 20px;
      display: block;
    }
    
    .create-btn {
      background: #0052D9;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      font-size: 16px;
    }
  }
}
</style>