{"timestamp": "2025-08-22T17:34:39.015Z", "summary": {"totalFiles": 8, "totalSize": 86470, "compressedSize": 60529, "compressionRatio": "30.00", "criticalCssSize": 3798}, "files": [{"path": "/Users/<USER>/IdeaProjects/air2/src/styles/components.scss", "relativePath": "styles/components.scss", "size": 8783, "name": "components.scss"}, {"path": "/Users/<USER>/IdeaProjects/air2/src/styles/critical.scss", "relativePath": "styles/critical.scss", "size": 5646, "name": "critical.scss"}, {"path": "/Users/<USER>/IdeaProjects/air2/src/styles/global.scss", "relativePath": "styles/global.scss", "size": 30871, "name": "global.scss"}, {"path": "/Users/<USER>/IdeaProjects/air2/src/styles/mixins.scss", "relativePath": "styles/mixins.scss", "size": 8716, "name": "mixins.scss"}, {"path": "/Users/<USER>/IdeaProjects/air2/src/styles/performance.scss", "relativePath": "styles/performance.scss", "size": 5711, "name": "performance.scss"}, {"path": "/Users/<USER>/IdeaProjects/air2/src/styles/responsive.scss", "relativePath": "styles/responsive.scss", "size": 8863, "name": "responsive.scss"}, {"path": "/Users/<USER>/IdeaProjects/air2/src/styles/utilities.scss", "relativePath": "styles/utilities.scss", "size": 11616, "name": "utilities.scss"}, {"path": "/Users/<USER>/IdeaProjects/air2/src/styles/variables.scss", "relativePath": "styles/variables.scss", "size": 6264, "name": "variables.scss"}], "criticalCss": {"path": "/Users/<USER>/IdeaProjects/air2/dist/css/critical.css", "size": 3798}, "unusedCss": {"usedClasses": ["dressup-result", "result-preview", "result-image", "compare-toggle", "compare-btn", "{", "comparison-view", "comparison-container", "comparison-item", "comparison-image", "comparison-label", "comparison-divider", "action-buttons", "action-btn", "secondary", "btn-icon", "btn-text", "primary", "filter-selector", "filter-header", "filter-title", "filter-close", "filter-list", "filter-item", "filter-preview", "filter-thumb", "filter-overlay", "filter-name", "filter-actions", "filter-btn", "cancel", "confirm", "save-modal", "save-content", "save-header", "save-title", "save-options", "save-option", "option-icon", "option-info", "option-title", "option-desc", "share-modal", "share-content", "share-header", "share-title", "share-platforms", "platform-item", "platform-icon", "wechat", "platform-name", "weibo", "qq", "copy", "lazy-image-container", "[", "loading-placeholder", "loading-content", "loading-spinner", "placeholder-image", "default-placeholder", "placeholder-icon", "error-placeholder", "error-content", "error-icon", "error-text", "theme-toggle", "toggle-container", "toggle-track", "toggle-thumb", "theme-icon", "theme-label", "label-text", "theme-selector", "selector-header", "selector-title", "theme-options", "theme-option", "option-content", "option-name", "option-check", "theme-mask", "virtual-list", "virtual-list-phantom", "virtual-list-content", "virtual-list-item", "dressup-container", "custom-navbar", "navbar-content", "nav-left", "back-icon", "nav-title", "nav-right", "save-btn", "main-content", "result-section", "photo-preview-section", "preview-container", "preview-image", "processing-overlay", "loading-animation", "loading-circle", "loading-text", "loading-progress", "progress-bar", "progress-fill", "progress-text", "empty-preview", "empty-icon", "empty-text", "upload-actions", "upload-btn", "clothes-drawer", "drawer-handle", "handle-bar", "drawer-title", "drawer-content", "category-tabs", "tab-item", "tab-text", "clothes-grid", "grid-container", "clothes-item", "clothes-image", "clothes-info", "clothes-name", "clothes-price", "select-indicator", "home-container", "app-title", "navbar-actions", "theme-toggle-btn", "avatar-btn", "avatar", "quick-action-section", "section-title", "action-cards", "action-card", "primary-card", "card-icon", "card-title", "card-desc", "secondary-card", "recommendation-section", "section-header", "more-btn", "recommendation-list", "recommendation-item", "item-image", "item-info", "item-name", "item-price", "category-section", "category-grid", "category-item", "category-icon", "category-name", "content", "logo", "text-area", "title", "profile-container", "nav-actions", "setting-icon", "user-info-card", "user-avatar-section", "user-avatar", "avatar-edit-btn", "edit-icon", "user-details", "user-name", "user-level", "user-stats", "stat-item", "stat-number", "stat-label", "menu-section", "menu-group", "menu-item", "menu-icon", "menu-text", "menu-arrow", "vip-badge", "works-section", "works-grid", "work-item", "work-image", "work-overlay", "work-date", "empty-works", "create-btn", "wardrobe-page", "search-section", "search-bar", "search-input", "search-icon", "filter-buttons", "clothes-virtual-list", "clothes-category", "clothes-actions", "favorite", "try-on", "loading", "back-top"], "unusedClasses": [], "usageRate": 204}, "recommendations": [{"type": "compression", "message": "建议启用更强的CSS压缩", "priority": "high"}]}