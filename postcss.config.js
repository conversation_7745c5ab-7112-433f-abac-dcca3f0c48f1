module.exports = {
  plugins: {
    // 自动添加浏览器前缀
    autoprefixer: {
      overrideBrowserslist: [
        'Android >= 4.4',
        'iOS >= 9',
        'Chrome >= 60',
        'Safari >= 10',
        'Firefox >= 60'
      ],
      grid: true
    },
    
    // CSS压缩优化
    ...(process.env.NODE_ENV === 'production' ? {
      cssnano: {
        preset: ['default', {
          // 保留重要注释
          discardComments: {
            removeAll: false,
            removeAllButFirst: true
          },
          // 优化calc()函数
          calc: {
            precision: 3
          },
          // 合并相同规则
          mergeRules: true,
          // 优化字体权重
          minifyFontValues: true,
          // 优化渐变
          minifyGradients: true,
          // 压缩选择器
          minifySelectors: true,
          // 标准化空白字符
          normalizeWhitespace: false,
          // 优化SVG
          svgo: {
            plugins: [
              {
                name: 'preset-default',
                params: {
                  overrides: {
                    removeViewBox: false,
                  },
                },
              },
            ],
          }
        }]
      }
    } : {})
  }
}
