import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        // 自动导入全局变量和混入
        additionalData: `
          @use "src/styles/variables" as *;
          @use "src/styles/mixins" as *;
        `,
        // 启用现代API
        api: 'modern-compiler',
        // 优化性能
        silenceDeprecations: ['legacy-js-api'],
      }
    },
    // CSS模块化配置
    modules: {
      localsConvention: 'camelCase',
      generateScopedName: '[name]__[local]___[hash:base64:5]'
    },
    // PostCSS配置
    postcss: {
      plugins: [
        // 自动添加浏览器前缀
        require('autoprefixer')({
          overrideBrowserslist: [
            'Android >= 4.4',
            'iOS >= 9'
          ]
        }),
        // CSS压缩优化
        require('cssnano')({
          preset: ['default', {
            discardComments: {
              removeAll: true
            },
            normalizeWhitespace: false
          }]
        })
      ]
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@styles': resolve(__dirname, 'src/styles'),
      '@components': resolve(__dirname, 'src/components'),
      '@utils': resolve(__dirname, 'src/utils')
    }
  },
  build: {
    // CSS代码分割
    cssCodeSplit: true,
    // 资源内联阈值
    assetsInlineLimit: 4096,
    rollupOptions: {
      output: {
        // CSS文件命名
        assetFileNames: (assetInfo) => {
          if (assetInfo.name.endsWith('.css')) {
            return 'css/[name].[hash][extname]'
          }
          return 'assets/[name].[hash][extname]'
        }
      }
    }
  }
})
